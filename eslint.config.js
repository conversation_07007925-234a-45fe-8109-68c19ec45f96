"use strict";
/** @type {import('eslint').Linter.FlatConfig[]} */
const typescriptEslint = require("@typescript-eslint/eslint-plugin");
const typescriptParser = require("@typescript-eslint/parser");
const sonarjs = require("eslint-plugin-sonarjs");
const eslintPluginUnicorn = require("eslint-plugin-unicorn");

module.exports = [
    {
        files: ["packages/**/src/**/*.ts"],
        languageOptions: {
            ecmaVersion: 2022,
            sourceType: "module",
            parser: typescriptParser,
            parserOptions: {
                tsconfigRootDir: __dirname,
                project: ["./tsconfig.lint.json"],
            },
            globals: {
                node: true,
                es2022: true
            },
        },
        plugins: {
            "@typescript-eslint": typescriptEslint,
            sonarjs,
            unicorn: eslintPluginUnicorn,
        },
        rules: {
            "@typescript-eslint/ban-ts-comment": "off",
            "@typescript-eslint/no-var-requires": "off",
            "@typescript-eslint/interface-name-prefix": "off",
            "@typescript-eslint/explicit-function-return-type": "off",
            "@typescript-eslint/no-explicit-any": "off",
            "@typescript-eslint/no-use-before-define": "off",
            "@typescript-eslint/no-non-null-assertion": "off",
            "@typescript-eslint/no-unused-vars": "off",
            "@typescript-eslint/explicit-module-boundary-types": "off",
            "@typescript-eslint/no-empty-function": "off",
            "@typescript-eslint/ban-types": "off",
            "@typescript-eslint/no-inferrable-types": "off",
            "no-return-await": "off",
            "quotes": ["error", "double"],
            "sonarjs/unused-import": "error",
        }
    },
]
