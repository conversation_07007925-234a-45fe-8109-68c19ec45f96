# Skywind Management API server

## Release commands:

### New release:
* releaseV param should be a number like 5.36
* lernaV param should be a number from lerna.json like 2.120.0
```sh
$ ./scripts/new-release.sh releaseV lernaV
```

### Next release:
* releaseV param should be a number like 5.37
* lernaV param should be a number from lerna.json like 2.121.0
```sh
$ ./scripts/next-release.sh releaseV lernaV
```

## Install Project
```sh
$ pnpm install
$ lerna run compile
$ lerna run version
```
