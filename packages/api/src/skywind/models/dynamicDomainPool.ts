import { Model, ModelStatic, DataTypes, InferAttributes, InferCreationAttributes } from "sequelize";
import { sequelize as db } from "../storage/db";
import {
    DynamicDomainPoolAttributes,
    DynamicDomainPoolItemAttributes
} from "../entities/domainPool";
import { getDynamicDomainModel } from "./dynamicDomain";

const DynamicDomainModel = getDynamicDomainModel();

export interface DynamicDomainPoolDBInstance extends Model<
    InferAttributes<DynamicDomainPoolDBInstance>,
    InferCreationAttributes<DynamicDomainPoolDBInstance>
>, DynamicDomainPoolAttributes {}

type IDynamicDomainPoolModel = ModelStatic<DynamicDomainPoolDBInstance>;

const DynamicDomainPoolModel: IDynamicDomainPoolModel =
    db.define<DynamicDomainPoolDBInstance, DynamicDomainPoolAttributes>("DynamicDomainPoolModel", {
            id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                autoIncrement: true,
                primaryKey: true,
                field: "id"
            },
            name: {
                type: DataTypes.STRING,
                allowNull: false,
                unique: true,
                field: "name"
            },
            domainWatcherAdapterId: {
                type: DataTypes.STRING,
                allowNull: true,
                field: "domain_watcher_adapter_id"
            },
            createdAt: {
                type: DataTypes.DATE,
                field: "created_at",
            },
            updatedAt: {
                type: DataTypes.DATE,
                field: "updated_at",
            },
        },
        {
            tableName: "dynamic_domain_pools",
            timestamps: true
        });

export interface DynamicDomainPoolItemDBInstance extends Model<
    InferAttributes<DynamicDomainPoolItemDBInstance>,
    InferCreationAttributes<DynamicDomainPoolItemDBInstance>
>,
    DynamicDomainPoolItemAttributes {
}
type IDynamicDomainPoolItemModel = ModelStatic<DynamicDomainPoolItemDBInstance>;

const DynamicDomainPoolItemModel: IDynamicDomainPoolItemModel =
    db.define<DynamicDomainPoolItemDBInstance, DynamicDomainPoolItemAttributes>("DynamicDomainPoolItem", {
        isActive: {
            type: DataTypes.BOOLEAN,
            defaultValue: true,
            field: "is_active"
        },
        dynamicDomainPoolId: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            references: {
                model: DynamicDomainPoolModel,
                key: "id"
            },
            field: "dynamic_domain_pool_id"
        },
        dynamicDomainId: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            references: {
                model: DynamicDomainModel,
                key: "id"
            },
            field: "dynamic_domain_id"
        },
        blockedDate: {
            type: DataTypes.DATE,
            allowNull: true,
            field: "blocked_date"
        },
    }, {
        tableName: "dynamic_domain_pools_dynamic_domains",
        timestamps: false
    });

DynamicDomainPoolModel.belongsToMany(DynamicDomainModel, {
    through: DynamicDomainPoolItemModel,
    as: "domains",
    foreignKey: "dynamic_domain_pool_id",
    otherKey: "dynamic_domain_id"
});

DynamicDomainModel.belongsToMany(DynamicDomainPoolModel, {
    through: DynamicDomainPoolItemModel,
    as: "pools",
    foreignKey: "dynamic_domain_id",
    otherKey: "dynamic_domain_pool_id"
});

export const getDynamicDomainPoolModel = () => DynamicDomainPoolModel;
export const getDynamicDomainPoolItemModel = () => DynamicDomainPoolItemModel;
