import { Model, DataTypes, InferAttributes, InferCreationAttributes, CreationOptional } from "sequelize";
import { sequelize as db } from "../storage/db";
import { DynamicDomain, DomainStatus, DomainInfo } from "../entities/domain";

export class DynamicDomainModel extends Model<
    InferAttributes<DynamicDomainModel>,
    InferCreationAttributes<DynamicDomainModel>
> {
    declare id: CreationOptional<number>;
    declare domain: string;
    declare description: string | null;
    declare provider: string | null;
    declare status: DomainStatus;
    declare expiryDate: Date | null;
    declare info: DomainInfo | null;
    declare environment: string | null;
    declare createdAt: CreationOptional<Date>;
    declare updatedAt: CreationOptional<Date>;

    public toInfo(): DynamicDomain {
        return {
            id: this.id,
            domain: this.domain,
            description: this.description,
            provider: this.provider,
            status: this.status,
            expiryDate: this.expiryDate,
            info: this.info,
            environment: this.environment,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt
        };
    };
}

DynamicDomainModel.init(
    {
        id: { field: "id", type: DataTypes.INTEGER, allowNull: false, autoIncrement: true, primaryKey: true },
        domain: { field: "domain", type: DataTypes.STRING, allowNull: false, unique: true },
        description: { field: "description", type: DataTypes.TEXT, allowNull: true },
        provider: { field: "provider", type: DataTypes.STRING, allowNull: true },
        status: { field: "status", type: DataTypes.ENUM(...Object.values(DomainStatus)), allowNull: false, defaultValue: DomainStatus.ACTIVE },
        expiryDate: { field: "expiry_date", type: DataTypes.DATE, allowNull: true },
        info: { field: "info", type: DataTypes.JSONB, allowNull: true },
        environment: { field: "environment", type: DataTypes.STRING, allowNull: false },
        createdAt: { field: "created_at", type: DataTypes.DATE },
        updatedAt: { field: "updated_at", type: DataTypes.DATE },
    },
    {
        tableName: "dynamic_domains",
        sequelize: db,
        underscored: true,
    }
);

export function getDynamicDomainModel() {
    return DynamicDomainModel;
}
