import "./wallet";
import createApplication, { startApplicationServer } from "./bootstrap/express";
import { defineRoutes } from "./api/routers";
import { Server } from "node:http";
import { lazy } from "@skywind-group/sw-utils";
import { installProcessHandlers } from "./bootstrap/common";
import config from "./config";

export const application = lazy(() => defineRoutes(createApplication()));

export async function startServer(port = 3000): Promise<Server> {
    installProcessHandlers();
    const name = config.server.getName() || "Management";
    return startApplicationServer(await application.get(), name, port);
}
