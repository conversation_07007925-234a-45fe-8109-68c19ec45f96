import { auditable, authenticate, authorize, decodePid, getEntity, validate, getBooleanParamFromRequestQuery } from "./middleware/middleware";
import * as express from "express";
import { getDynamicDomainPoolService } from "../services/dynamicDomainPool";
import { KeyEntityHolder } from "../services/security";
import { EntityDynamicDomainPoolService } from "../services/entityDynamicDomainPool";

const router: express.Router = express.Router();

async function getDynamicDomainPools(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const pools = await getDynamicDomainPoolService().findAll();
        res.send(pools);
    } catch (err) {
        next(err);
    }
}

async function createDynamicDomainPool(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const pool = await getDynamicDomainPoolService().create(req.body);
        res.status(201).send(pool);
    } catch (err) {
        next(err);
    }
}

async function getDynamicDomainPool(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const pool = await getDynamicDomainPoolService().findById(req.params.poolId);
        res.send(pool);
    } catch (err) {
        next(err);
    }
}

async function updateDynamicDomainPool(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const pool = await getDynamicDomainPoolService().update(req.params.poolId, req.body);
        res.send(pool);
    } catch (err) {
        next(err);
    }
}

async function removeDynamicDomainPool(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        await getDynamicDomainPoolService().remove(req.params.poolId);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function addDynamicDomainPoolItem(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const { poolId, domainId } = req.params;
        const pool = await getDynamicDomainPoolService().addDomain(poolId, domainId);
        res.send(pool);
    } catch (err) {
        next(err);
    }
}

async function removeDynamicDomainPoolItem(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const { poolId, domainId } = req.params;
        await getDynamicDomainPoolService().removeDomain(poolId, domainId);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function enableDynamicDomainPoolItem(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const { poolId, domainId } = req.params;
        await getDynamicDomainPoolService().enableDomain(poolId, domainId);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function disableDynamicDomainPoolItem(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const { poolId, domainId } = req.params;
        await getDynamicDomainPoolService().disableDomain(poolId, domainId);
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

async function getEntityDynamicDomainPool(
    req: express.Request & KeyEntityHolder,
    res: express.Response,
    next: express.NextFunction
) {
    try {
        const inherited = getBooleanParamFromRequestQuery(req, "inherited", false);
        const entity = getEntity(req);
        const entityDynamicDomainPoolService = new EntityDynamicDomainPoolService(entity);
        const pool = await entityDynamicDomainPoolService.getPool(inherited);
        res.send(pool);
    } catch (err) {
        next(err);
    }
}

async function addEntityDynamicDomainPool(
    req: express.Request & KeyEntityHolder,
    res: express.Response,
    next: express.NextFunction
) {
    try {
        const entity = getEntity(req);
        const entityDynamicDomainPoolService = new EntityDynamicDomainPoolService(entity);
        const pool = await entityDynamicDomainPoolService.addPool(req.params.poolId);
        res.send(pool);
    } catch (err) {
        next(err);
    }
}

async function removeEntityDynamicDomainPool(
    req: express.Request & KeyEntityHolder,
    res: express.Response,
    next: express.NextFunction
) {
    try {
        const entity = getEntity(req);
        const entityDynamicDomainPoolService = new EntityDynamicDomainPoolService(entity);
        await entityDynamicDomainPoolService.removePool();
        res.status(204).end();
    } catch (err) {
        next(err);
    }
}

// dynamic domain pool management
router.get("/domain-pools/dynamic", authenticate, authorize, getDynamicDomainPools);
router.post("/domain-pools/dynamic",
    authenticate,
    authorize,
    validate({
        name: { isString: true, notEmpty: true },
        domainWatcherAdapterId: { isString: true, optional: true },
        domains: { isDomainPoolItemArray: true, optional: true }
    }),
    decodePid({ forceReturnIfNumber: true, keysToParse: ["domains"], ignoredKeys: ["domainWatcherAdapterId"] }),
    auditable,
    createDynamicDomainPool);
router.get("/domain-pools/:poolId/dynamic",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    getDynamicDomainPool);
router.patch("/domain-pools/:poolId/dynamic",
    authenticate, authorize,
    validate({
        name: { isString: true, optional: true },
        domainWatcherAdapterId: { isString: true, optional: true },
        domains: { isDomainPoolItemArray: true, optional: true }
    }),
    decodePid({ forceReturnIfNumber: true, keysToParse: ["domains"], ignoredKeys: ["domainWatcherAdapterId"] }),
    auditable,
    updateDynamicDomainPool);
router.delete("/domain-pools/:poolId/dynamic",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    removeDynamicDomainPool);

router.put("/domain-pools/:poolId/dynamic/:domainId",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    addDynamicDomainPoolItem);
router.delete("/domain-pools/:poolId/dynamic/:domainId",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    removeDynamicDomainPoolItem);
router.put("/domain-pools/:poolId/dynamic/:domainId/enable",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    enableDynamicDomainPoolItem);
router.put("/domain-pools/:poolId/dynamic/:domainId/disable",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    auditable,
    disableDynamicDomainPoolItem);

router.get("/entities/:path/domain-pools/dynamic",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    getEntityDynamicDomainPool);
router.put("/entities/:path/domain-pools/:poolId/dynamic",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    addEntityDynamicDomainPool);
router.delete("/entities/:path/domain-pools/dynamic",
    authenticate, authorize,
    decodePid({ forceReturnIfNumber: true }),
    removeEntityDynamicDomainPool);

export default router;
