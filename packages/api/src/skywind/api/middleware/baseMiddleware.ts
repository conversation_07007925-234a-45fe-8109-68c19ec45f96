import { IpHolder } from "../../services/security";
import { findIp, requestLogData } from "../../utils/requestHelper";
import { NextFunction, Request, Response } from "express";
import { ValidationError } from "../../errors";
import { ContextVariables } from "../../utils/contextVariables";
import { X_ACCESS_TOKEN, X_PLAYER_TOKEN, X_TERMINAL_TOKEN } from "../../utils/common";

export function createRequestLogger(logger): (req: Request, res: Response, next: NextFunction) => any {
    return (req: Request & IpHolder, res: Response, next: NextFunction): any => {
        if (req.originalUrl.includes("/version") || req.originalUrl.includes("/health")) {
            return next();
        }
        logger.info({ reqData: requestLogData(req) }, "Http request");
        next();
    };
}

export function setUserAuthContext(req: Request & IpHolder, res: Response, next: NextFunction) {
    const accessToken = req.header(X_ACCESS_TOKEN);
    ContextVariables.setUserAuth(accessToken);
    next();
}

export function setTerminalAuthContext(req: Request, _res: Response, next: NextFunction) {
    const terminalToken = req.header(X_TERMINAL_TOKEN);
    ContextVariables.setTerminalAuth(terminalToken);
    next();
}

export function setPlayerAuthContext(req: Request & IpHolder, res: Response, next: NextFunction) {
    const playerLoginToken = req.header(X_PLAYER_TOKEN);
    ContextVariables.setPlayerAuth(playerLoginToken);
    next();
}

const DEFAULT_MAX_AGE_OPTIONS = (30 * 24 * 60 * 60).toString();
const UNSUPPORTED_CONTENT_TYPES = ["multipart/form-data"];

export function addHeaderCORS(req: Request, res: Response, next: NextFunction) {
    if (res.headersSent) {
        next();
        return;
    }

    res.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS, DELETE, PATCH, PUT");
    /* TODO:
     * we currently use "*" (all http origins),
     * it must be replaced with the origins white list, when implemented
     */
    res.setHeader("Access-Control-Allow-Origin", "*");
    if (req.method === "OPTIONS") {
        /* TODO:
         * list of headers must be defined
         */
        if (req.header("Access-Control-Request-Headers")) {
            res.setHeader("Access-Control-Allow-Headers", req.header("Access-Control-Request-Headers"));
        }
        res.setHeader("Access-Control-Max-Age", DEFAULT_MAX_AGE_OPTIONS);

        res.status(204).end();
    } else {
        next();
    }
}

export function addHeaderCacheControl(req: Request, res: Response, next: NextFunction) {
    if (req.method === "GET") {
        res.setHeader("Cache-Control", "no-cache, max-age=0");
    }
    next();
}

export function validateContentType(req: Request, res: Response, next: NextFunction) {
    const contentType = req.header("content-type");
    if (!contentType) {
        return next();
    }
    if (UNSUPPORTED_CONTENT_TYPES.includes(contentType.split(";")[0])) {
        return next(new ValidationError("content-type should be \"application/json\""));
    }
    next();
}

export function resolveIp(req: Request & IpHolder, res: Response, next: NextFunction) {
    req.resolvedIp = findIp(req);
    next();
}

export function sanitizeQueryParams(req: Request, res: Response, next: NextFunction) {
    req.query = Object.keys(req.query).reduce((query, param) => ({
        ...query,
        [param]: Array.isArray(req.query[param]) ? req.query[param][0] : req.query[param]
    }), {});
    next();
}
