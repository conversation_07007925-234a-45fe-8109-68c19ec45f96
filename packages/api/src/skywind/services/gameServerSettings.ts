import { Op } from "sequelize";
import {
    CreateDataBody,
    GameServerSettings, GameServerSettingsModelData, RangeTypeInstance,
    UpdateData
} from "../entities/gameServerSettings";
import * as Errors from "../errors";
import { Models } from "../models/models";

const model = Models.GameServerSettingsModel;

export interface GameServerSettingsService {
    findAll(): Promise<GameServerSettings[]>;

    create(data: CreateDataBody): Promise<GameServerSettings>;

    update(name: string, data: UpdateData): Promise<GameServerSettings>;

    findByName(name: string): Promise<GameServerSettings>;

    remove(name: string): Promise<void>;
}

class GameServerSettingsServiceImpl implements GameServerSettingsService {
    public async findAll(): Promise<GameServerSettings[]> {
        const instances = await model.findAll();

        return instances.map(instance => instance.toInfo());
    }

    public async findByName(name: string): Promise<GameServerSettings> {
        const instance = await model.findOne({
            where: {
                name: { [Op.eq]: name }
            }
        });

        if (!instance) {
            return Promise.reject(new Errors.GameServerSettingsNotFound());
        }

        return instance.toInfo();
    }

    public async create(data: CreateDataBody): Promise<GameServerSettings> {
        const cleanedData = this.validateCreateData(data);

        try {
            const instance = await model.create(cleanedData);
            return instance.toInfo();
        } catch (err) {
            if (err.name === "SequelizeUniqueConstraintError") {
                return Promise.reject(new Errors.GameServerSettingsExists());
            }
            if (err.name === "SequelizeExclusionConstraintError") {
                return Promise.reject(new Errors.RangeIntersectError());
            }
            // continue with the unknown error
            return Promise.reject(err);
        }
    }

    public async update(name: string, data: UpdateData): Promise<GameServerSettings> {
        const cleanedData = this.validateUpdateData(data);
        if (Object.keys(cleanedData).length === 0) {
            throw new Errors.GameServerSettingsNotFound();
        }
        try {
            const [countElements, [instance]] = await model.update(cleanedData, {
                where: {
                    name: { [Op.eq]: name }
                },
                returning: true,
            });

            if (!countElements) {
                return Promise.reject(new Errors.GameServerSettingsNotFound());
            }

            return instance.toInfo();
        } catch (err) {
            if (err.name === "SequelizeUniqueConstraintError") {
                return Promise.reject(new Errors.GameServerSettingsExists());
            }
            // continue with the unknown error
            return Promise.reject(err);
        }
    }

    public async remove(name: string): Promise<void> {
        const countElements = await model.destroy({
            where: {
                name: { [Op.eq]: name }
            }
        });

        if (!countElements) {
            return Promise.reject(new Errors.GameServerSettingsNotFound());
        }
    }

    private validateCreateData(data: CreateDataBody): GameServerSettingsModelData {
        const errorMessages = [];

        if (!data.name || typeof data.name !== "string") {
            errorMessages.push("name should be a non-empty string");
        }

        if (isInvalidBigNumberRange(data.roundIdRange)) {
            errorMessages.push("roundIdRange should be an array of two big numbers" +
                " and the difference between two bounds should not be less then one");
        }

        if (isInvalidBigNumberRange(data.sessionIdRange)) {
            errorMessages.push("sessionIdRange should be an array of two big numbers" +
                " and the difference between two bounds should not be less then one");
        }

        if (errorMessages.length) {
            throw new Errors.ValidationError(errorMessages);
        }

        const cleanedData: GameServerSettingsModelData = {
            name: data.name,
            roundIdRange: prepareRange(data.roundIdRange),
            sessionIdRange: prepareRange(data.sessionIdRange)
        };

        if (data.description) {
            cleanedData.description = data.description;
        }

        return cleanedData;
    }

    private validateUpdateData(data: UpdateData): GameServerSettingsModelData {
        const errorMessages = [];

        if (data.name && typeof data.name !== "string") {
            errorMessages.push("name should be a string");
        }

        if (data.description && typeof data.description !== "string") {
            errorMessages.push("description should be a string");
        }

        if (data.roundIdRange !== undefined && isInvalidBigNumberRange(data.roundIdRange)) {
            errorMessages.push("roundIdRange should be an array of two big numbers" +
                " and the difference between two bounds should not be less then one");
        }

        if (data.sessionIdRange !== undefined && isInvalidBigNumberRange(data.sessionIdRange)) {
            errorMessages.push("sessionIdRange should be an array of two big numbers" +
                " and the difference between two bounds should not be less then one");
        }

        if (errorMessages.length) {
            throw new Errors.ValidationError(errorMessages);
        }

        const cleanedData: GameServerSettingsModelData = {};

        if (data.name) {
            cleanedData.name = data.name;
        }

        if (data.roundIdRange) {
            cleanedData.roundIdRange = prepareRange(data.roundIdRange);
        }

        if (data.sessionIdRange) {
            cleanedData.sessionIdRange = prepareRange(data.sessionIdRange);
        }

        if (data.description) {
            cleanedData.description = data.description;
        }

        return cleanedData;
    }
}

function isInvalidBigNumberRange(range: [string | number, string | number]): boolean {
    return (!Array.isArray(range)
        || range.length !== 2
        || range.some(item => Number.isNaN(+item))
        || +range[0] === +range[1]
        || Math.abs((+range[0]) - (+range[1])) <= 1);
}

function prepareRange(range: [any, any],
                      inclusive: [boolean, boolean] = [true, false]): [RangeTypeInstance, RangeTypeInstance] {
    range = range.sort((a, b) => a - b);
    return [
        { value: range[0], inclusive: inclusive[0] },
        { value: range[1], inclusive: inclusive[1] }
    ];
}

let service: GameServerSettingsService;

export function getGameServerSettingsService(): GameServerSettingsService {
    if (!service) {
        service = new GameServerSettingsServiceImpl();
    }
    return service;
}
