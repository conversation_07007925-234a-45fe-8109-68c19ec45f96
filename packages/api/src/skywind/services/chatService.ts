import EntityCache from "../cache/entity";
import { BrandEntity } from "../entities/brand";
import { EntityGame, GameSettings, Live } from "../entities/game";
import { PlayerInfo } from "../entities/playerInfo";
import * as EntityGameCache from "../cache/entityGame";
import { getPlayerInfoService } from "./playerInfo";
import { EntitySettings, EntitySettingsUpdate } from "../entities/settings";
import { getEntitySettings } from "./settings";
import { isChatDisabled } from "../utils/chatHelper";
import { StartGameTokenData } from "@skywind-group/sw-wallet-adapter-core";

export class ChatService {
    public static async getSettings(decoded: StartGameTokenData) {
        const brand = await EntityCache.findById<BrandEntity>(decoded.brandId);

        const [entityGame, playerInfo]: [EntityGame, PlayerInfo] = await Promise.all([
            EntityGameCache.findOne(brand, decoded.gameCode),
            getPlayerInfoService().getPlayerInfo(decoded.playerCode, decoded.brandId)
        ]);

        const entitySettings: EntitySettings = await getEntitySettings(brand.path);

        const liveSettings = entityGame.game.features.live;
        let channel = this.mergeAndBuildChannel(entityGame.settings, entitySettings, liveSettings);

        return {
            tableSettings: {
                channel,
                tableId: liveSettings.tableId,
                provider: liveSettings.provider,
            },
            chatSettings: {
                noBetNoChat: playerInfo.noBetNoChat,
                hasWarn: playerInfo.hasWarn,
                isPublicChatBlock: playerInfo.isPublicChatBlock,
                isPrivateChatBlock: playerInfo.isPrivateChatBlock,
                isPublicChatDisabled: isChatDisabled(liveSettings, "public", "Chat"),
                isPrivateChatDisabled: isChatDisabled(liveSettings, "private", "PrivateChat"),
            },
            player: {
                isVip: playerInfo.isVip,
                isTracked: playerInfo.isTracked,
                nickname: playerInfo.nickname,
            }
        }
    }

    public static mergeAndBuildChannel(entityGameSettings: GameSettings,
                                       entitySettings: EntitySettingsUpdate,
                                       liveSettings: Live): string {
        const { tableId, provider } = liveSettings;
        const gameSettings = {
            social: entityGameSettings.social || false,
            useSocialCasinoOperator: entityGameSettings.useSocialCasinoOperator || "",
        };

        Object.assign(gameSettings, entitySettings);
        Object.assign(gameSettings, liveSettings);

        let channel = `${provider}:${tableId}`;
        if (gameSettings.social) {
            channel += ":social";
            const useOperator = gameSettings.useSocialCasinoOperator;
            if (useOperator !== "") {
                channel += `:${useOperator}`;
            }
        }
        return channel;
    }

    public static getDefaultSettings() {
        const chatSettings = {
            noBetNoChat: false,
            hasWarn: false,
            isPublicChatBlock: false,
            isPrivateChatBlock: false,
            isPublicChatDisabled: true,
            isPrivateChatDisabled: true,
        };
        const player = {
            isVip: false,
            isTracked: false,
            nickname: "",
        };
        const tableSettings = {
            channel: "",
            tableId: "",
            provider: "",
        };
        return {
            chatSettings,
            player,
            tableSettings,
        };
    }
}
