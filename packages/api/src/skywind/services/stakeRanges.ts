import { Models } from "../models/models";
import { ValidationError } from "../errors";
import { StakeRange } from "../models/stakeRange";

class StakeRangesService {
    public async update(currency: string, coinBets: number[], lowerStakes: number[] | undefined) {
        const [numberOfElements, updatedStakeRanges] = await Models.StakeRangeModel.update({
            currency,
            coinBets,
            lowerStakes,
        }, { where: { currency }, returning: true });

        if (!numberOfElements) {
            throw new ValidationError("Cannot find stake ranges for that currency");
        }
        return updatedStakeRanges[0];
    }

    public async findAll(): Promise<StakeRange[]> {
        const stakeRanges = await Models.StakeRangeModel.findAll();
        return stakeRanges.map(stake => stake.toInfo());
    }

    public async create(currency: string, coinBets: number[], lowerStakes: number[] | undefined) {
        return Models.StakeRangeModel.create({ currency, coinBets, lowerStakes });
    }

    public async findOne(currency: string) {
        return Models.StakeRangeModel.findOne({ where: { currency } });
    }

    public async destroy(currency: string): Promise<void> {
        await Models.StakeRangeModel.destroy({ where: { currency } });
    }
}

export default new StakeRangesService();
