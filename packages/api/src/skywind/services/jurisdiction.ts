import { AllowedJackpotConfigurationLevel, CreateData, Jurisdiction, UpdateData } from "../entities/jurisdiction";
import { ForeignKeyConstraintError, WhereOptions } from "sequelize";
import * as Errors from "../errors";
import { lazy } from "@skywind-group/sw-utils";
import { PagingHelper } from "../utils/paginghelper";
import { Models } from "../models/models";

const container = lazy<JurisdictionService>(() => new JurisdictionServiceImpl());

export function getJurisdictionService(): JurisdictionService {
    return container.get();
}

export interface JurisdictionService {
    create(data: CreateData): Promise<Jurisdiction>;

    findAll(query?: WhereOptions, limit?: number, offset?: number): Promise<Jurisdiction[]>;

    findOne(code: string): Promise<Jurisdiction>;

    update(code: string, data: UpdateData): Promise<Jurisdiction>;

    remove(code: string): Promise<void>;
}

export const JURISDICTION_CODE_LENGTH = {
    MIN: 2,
    MAX: 6
};

class JurisdictionServiceImpl implements JurisdictionService {

    private static model = Models.JurisdictionModel;

    public async create(data: CreateData): Promise<Jurisdiction> {
        const cleanedData = validateCreateData(data);

        try {
            const instance = await JurisdictionServiceImpl.model.create(cleanedData);
            return instance.toInfo();
        } catch (err) {
            if (err.name === "SequelizeUniqueConstraintError") {
                return Promise.reject(new Errors.ValidationError("Jurisdiction already exists"));
            }
            // continue with the unknown error
            return Promise.reject(err);
        }
    }

    public async update(code: string, data: UpdateData): Promise<Jurisdiction> {
        const jurisdiction = await JurisdictionServiceImpl.model.findOne({ where: { code } });

        if (!jurisdiction) {
            throw new Errors.ValidationError(`Jurisdiction not found - ${code}`);
        }

        const cleanedData = validateUpdateData(jurisdiction.toInfo(), data);

        try {
            await jurisdiction.update(cleanedData);

            return jurisdiction.toInfo();
        } catch (err) {
            if (err.name === "SequelizeUniqueConstraintError") {
                return Promise.reject(new Errors.ValidationError("Jurisdiction already exists"));
            }
            // continue with the unknown error
            return Promise.reject(err);
        }
    }

    public async remove(code: string): Promise<void> {
        try {
            const sqlQuery = {
                where: { code }
            };
            const countElements = await JurisdictionServiceImpl.model.destroy(sqlQuery);

            if (!countElements) {
                return Promise.reject(new Errors.ValidationError("Jurisdiction not found"));
            }
        } catch (err) {
            if (err instanceof ForeignKeyConstraintError) {
                return Promise.reject(new Errors.FailedToDeleteItemNeedForce());
            }
        }
    }

    public async findAll(query: WhereOptions = {}, limit = 100, offset = 0): Promise<Jurisdiction[]> {
        const sortBy = "created_at";
        const sortOrder = "DESC";

        return PagingHelper.findAndCountAll(JurisdictionServiceImpl.model, {
            where: query,
            order: [[sortBy, sortOrder]],
            offset,
            limit
        }, (item) => item.toInfo());
    }

    public async findOne(code: string): Promise<Jurisdiction> {
        const sqlQuery = {
            where: { code }
        };
        const instance = await JurisdictionServiceImpl.model.findOne(sqlQuery);

        if (!instance) {
            throw new Errors.ValidationError("Jurisdiction not found");
        }

        return instance.toInfo();
    }
}

function validateCreateData(data: CreateData): CreateData {
    const cleanedData: CreateData = {
        code: data.code,
        createdUserId: data.createdUserId,
        updatedUserId: data.updatedUserId
    };

    const errorMessages = [];
    if ("title" in data && typeof data.title !== "string") {
        errorMessages.push("title should be a string");
    }

    if (isInvalidJurisdictionCode(data.code)) {
        errorMessages.push(
            `code is required and should be a string with length between ${JURISDICTION_CODE_LENGTH.MIN} and ${JURISDICTION_CODE_LENGTH.MAX} and not contain whitespaces`); // tslint:disable-line
    }

    if (!data.settings) {
        errorMessages.push("settings is required");
    }

    if (data.settings && "maxTotalStake" in data.settings) {
        if (!Number.isFinite(+data.settings.maxTotalStake) || +data.settings.maxTotalStake <= 0) {
            errorMessages.push("Max total stake should be a positive number");
        }
    }

    if ("restrictedCountries" in data && "allowedCountries" in data) {
        errorMessages.push("Allowed only restricted or allowed countries");
    }

    if (Array.isArray(data.restrictedCountries) &&
        data.defaultCountry &&
        data.restrictedCountries.includes(data.defaultCountry)) {
        errorMessages.push("Default country cannot be in restricted countries");
    }

    if (Array.isArray(data.allowedCountries) &&
        data.defaultCountry &&
        !data.allowedCountries.includes(data.defaultCountry)) {
        errorMessages.push("Default country should be from allowed countries");
    }

    if (errorMessages.length) {
        throw new Errors.ValidationError(errorMessages);
    }

    if (data.description) {
        cleanedData.description = data.description;
    }

    if (data.settings) {
        cleanedData.settings = data.settings;
    }

    if (data.title) {
        cleanedData.title = data.title;
    }

    if (data.restrictedCountries) {
        cleanedData.restrictedCountries = data.restrictedCountries;
    }

    if (data.allowedCountries) {
        cleanedData.allowedCountries = data.allowedCountries;
    }

    if (data.defaultCountry) {
        cleanedData.defaultCountry = data.defaultCountry;
    }

    if (data.allowedJackpotConfigurationLevel !== undefined && data.allowedJackpotConfigurationLevel !== null) {
        cleanedData.allowedJackpotConfigurationLevel = data.allowedJackpotConfigurationLevel;
    } else {
        cleanedData.allowedJackpotConfigurationLevel = AllowedJackpotConfigurationLevel.NO_RESTRICTIONS;
    }

    return cleanedData;
}

function validateUpdateData(jurisdiction: Jurisdiction, data: UpdateData): UpdateData {
    const cleanedData: UpdateData = {
        updatedUserId: data.updatedUserId
    };

    if (data.title) {
        cleanedData.title = data.title;
    }

    const restrictedCountries = data.restrictedCountries || jurisdiction.restrictedCountries;
    const allowedCountries = data.allowedCountries || jurisdiction.allowedCountries;
    const defaultCountry = data.defaultCountry || jurisdiction.defaultCountry;

    if (Array.isArray(data.restrictedCountries) && Array.isArray(data.allowedCountries) ||
        Array.isArray(data.restrictedCountries) &&
        (Array.isArray(jurisdiction.allowedCountries) && data.allowedCountries !== null) ||
        Array.isArray(data.allowedCountries) &&
        (Array.isArray(jurisdiction.restrictedCountries) && data.restrictedCountries !== null)) {
        throw new Errors.ValidationError("Allowed only restricted or allowed countries");
    }

    if (data.restrictedCountries !== null && Array.isArray(restrictedCountries) &&
        defaultCountry && restrictedCountries.includes(defaultCountry)) {
        throw new Errors.ValidationError("Default country cannot be in restricted countries");
    }

    if (data.allowedCountries !== null && Array.isArray(allowedCountries) &&
        defaultCountry && !allowedCountries.includes(defaultCountry)) {
        throw new Errors.ValidationError("Default country should be from allowed countries");
    }

    if (data.code) {
        if (isInvalidJurisdictionCode(data.code)) {
            throw new Errors.ValidationError("code should be a string with length between " +
                `${JURISDICTION_CODE_LENGTH.MIN} and ${JURISDICTION_CODE_LENGTH.MAX} and not contain whitespaces`); // tslint:disable-line
        }
        cleanedData.code = data.code.trim();
    }

    if (data.description) {
        cleanedData.description = data.description;
    }

    if (data.settings) {
        if ("maxTotalStake" in data.settings) {
            if (!Number.isFinite(+data.settings.maxTotalStake) || +data.settings.maxTotalStake <= 0) {
                throw new Errors.ValidationError("Max total stake should be a positive number");
            }
        }

        cleanedData.settings = data.settings;
    }

    if (data.restrictedCountries || data.restrictedCountries === null) {
        cleanedData.restrictedCountries = data.restrictedCountries;
    }

    if (data.allowedCountries || data.allowedCountries === null) {
        cleanedData.allowedCountries = data.allowedCountries;
    }

    if (data.defaultCountry || data.defaultCountry === null) {
        cleanedData.defaultCountry = data.defaultCountry;
    }

    if (data.allowedJackpotConfigurationLevel !== undefined && data.allowedJackpotConfigurationLevel !== null) {
        cleanedData.allowedJackpotConfigurationLevel = data.allowedJackpotConfigurationLevel;
    }

    return cleanedData;
}

function isInvalidJurisdictionCode(code: string): boolean {
    return typeof code !== "string" ||
        code.includes(" ") ||
        code.length > JURISDICTION_CODE_LENGTH.MAX ||
        code.length < JURISDICTION_CODE_LENGTH.MIN;
}
