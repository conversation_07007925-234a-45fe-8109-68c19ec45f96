import { logging } from "@skywind-group/sw-utils";
import { DomainSource } from "./domainSource";
import { getTapkingAdapter } from "./tapkingAdapter";
import { DomainSources, DomainWatcherAdapter, DomainStatus } from "./types";
import { DomainWatcherAdapterNotSupportedError } from "../../errors";

interface DomainWatcherInfo {
    domainId?: number;
    status?: DomainStatus;
    sources?: DomainSource[];
}

export class DomainWatcher {
    private readonly domains: Map<string, DomainWatcherInfo> = new Map();

    constructor(private readonly adapter: DomainWatcherAdapter) {
    }

    public async update(domains: DomainSources) {
        const list = await this.adapter.list();
        for (const { domain, status } of list) {
            const existing = this.domains.get(domain);
            this.domains.set(domain, {
                domainId: existing?.domainId,
                status,
                sources: existing?.sources
            });
        }
        for (const domain of this.domains.keys()) {
            if (!domains.has(domain)) {
                await this.adapter.remove(domain);
                this.domains.delete(domain);
            }
        }
        for (const [domain, { domainId, sources }] of domains.entries()) {
            if (!this.domains.has(domain)) {
                await this.adapter.register(domain);
                this.domains.set(domain, { domainId, status: { accessStatus: "UNKNOWN" }, sources });
            }
        }
        for (const [domain, { domainId, status, sources }] of this.domains.entries()) {
            if (status?.accessStatus === "BLOCKED" && domainId && sources?.length > 0) {
                for (const source of sources) {
                    await source.setBlocked(status, this.adapter.adapterId);
                }
                await this.adapter.remove(domain);
                this.domains.delete(domain);
            }
        }
    }
}

function getAdapter(adapter: string, log?: logging.Logger): DomainWatcherAdapter {
    if (adapter === "tapking") {
        return getTapkingAdapter(log);
    }
    throw new DomainWatcherAdapterNotSupportedError(adapter);
}

export function getDomainWatcher(adapter: string, log?: logging.Logger): DomainWatcher {
    return new DomainWatcher(getAdapter(adapter, log));
}
