import { BaseEntity } from "../../entities/entity";
import * as EntityJurisdictionCache from "../../cache/entityJurisdiction";
import { getCurrencyExchange } from "../currencyExchange";

export async function buildDynamicMaxTotalStake(
    entity: BaseEntity,
    currency: string,
    skipJurisdictionFiltering?: boolean,
    operatorDynamicMaxTotalBetLimit?: number
): Promise<number> {
    if (skipJurisdictionFiltering) {
        return;
    }
    const jurisdiction = await EntityJurisdictionCache.findOne(entity);
    if (!jurisdiction) {
        return;
    }
    const { dynamicMaxTotalBetLimitEnabled, dynamicMaxTotalBetLimit, maxTotalStake } = jurisdiction?.settings ?? {};
    const { defaultTotalBet, defaultMaxTotalBet } = dynamicMaxTotalBetLimit ?? {};
    const dynamicMaxTotalBetLimitValid = defaultTotalBet && defaultMaxTotalBet;
    if (maxTotalStake || (dynamicMaxTotalBetLimitEnabled && (operatorDynamicMaxTotalBetLimit || dynamicMaxTotalBetLimitValid))) {
        const service = await getCurrencyExchange();
        const exchange = (amount: number, baseCurrency: string) => service.exchange(amount, baseCurrency, currency);
        if (dynamicMaxTotalBetLimitEnabled) {
            if (!dynamicMaxTotalBetLimitValid && operatorDynamicMaxTotalBetLimit) {
                return operatorDynamicMaxTotalBetLimit;
            }
            if (!operatorDynamicMaxTotalBetLimit && defaultTotalBet) {
                return exchange(+defaultTotalBet, dynamicMaxTotalBetLimit.currency || "EUR");
            }
            if (operatorDynamicMaxTotalBetLimit) {
                if (defaultMaxTotalBet) {
                    const exchangedDefaultMaxTotalBet = exchange(+defaultMaxTotalBet, dynamicMaxTotalBetLimit.currency || "EUR");
                    if (operatorDynamicMaxTotalBetLimit <= exchangedDefaultMaxTotalBet) {
                        return operatorDynamicMaxTotalBetLimit;
                    }
                    if (operatorDynamicMaxTotalBetLimit > exchangedDefaultMaxTotalBet) {
                        return exchangedDefaultMaxTotalBet;
                    }
                } else {
                    return operatorDynamicMaxTotalBetLimit;
                }
            }
        }
        return exchange(+maxTotalStake, "EUR");
    }
}
