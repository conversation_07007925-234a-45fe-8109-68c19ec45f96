import { v4 } from "uuid";
import { BaseEntity } from "../../entities/entity";
import { usingBonusDb } from "../../storage/redis";
import { CurrencyNotFoundError, ValidationError } from "../../errors";
import { Currencies } from "@skywind-group/sw-currency-exchange";
import { kafka, redis } from "@skywind-group/sw-utils";
import { gameFeaturesCache } from "../../cache/gameFeaturesCache";
import * as path from "path";
import loggerFactory from "../../utils/logger";
import config from "../../config";
import { OPERATION_ID, PlayerWalletImpl, RemoteWalletFacade, WalletFacade } from "@skywind-group/sw-management-wallet";
import { BonusFreebetsData, PlayerFreebetWallet } from "@skywind-group/sw-management-promo-wallet";
import { WALLET_OPERATION_NAME } from "@skywind-group/sw-management-playservice";
import { getEntitySettings } from "../settings";
import { BrandEntity } from "../../entities/brand";

export enum PlayerBonusType {
    FreeBet = "freebet"
}

interface CreatePlayerBonusDto {
    type: PlayerBonusType;
    title: string;
    description: string;
    playerCode: string;
    amount: number;
    gameCode: string | string[];
    coin: number;
    currency: string;
    expiresAt: string;
    externalId?: string;
}

enum PendingPlayerBonusEventType {
    Create = "createPlayerBonus",
    Cancel = "cancelPlayerBonus"
}

interface PendingPlayerBonusEvent {
    type: PendingPlayerBonusEventType;
    data: PlayerBonus;
    ts: string;
}

export interface PlayerBonus extends CreatePlayerBonusDto {
    id: string;
    createdAt: string;
}

const logger = loggerFactory("player-bonuses");

class PlayerBonusService {
    private readonly popPlayerBonusProc: redis.RedisProc;
    private static readonly recentBonusesHashKey = "player:recent-bonuses";
    private kafkaWriter: kafka.KafkaWriter;

    constructor() {
        this.popPlayerBonusProc = new redis.RedisProc(
            logger,
            path.resolve(__dirname, "../../../../resources/lua/popPlayerBonus.lua")
        );
    }

    public async createPlayerBonus(entity: BaseEntity, playerBonus: CreatePlayerBonusDto): Promise<PlayerBonus> {
        if (playerBonus.externalId) {
            const recentPlayerBonus = await this.getRecentPlayerBonus(entity.id, playerBonus.externalId);
            if (recentPlayerBonus) {
                return recentPlayerBonus;
            }
        }

        await this.validatePlayerBonus(entity, playerBonus);
        const bonusId = v4();
        const createdAt = new Date().toISOString();
        await usingBonusDb<void>(async (db) => {
            const multi = db.multi();
            const key = this.getPlayerBonusKey(entity.id, playerBonus.playerCode, playerBonus.type);
            multi.hset(key, bonusId, JSON.stringify({ ...playerBonus, createdAt }));
            const expireInSec = Math.round((new Date(playerBonus.expiresAt).getTime() - Date.now()) / 1000);
            multi.call("HEXPIRE", key, expireInSec, "FIELDS", 1, bonusId);
            // Store recent player bonuses that have an externalId
            if (playerBonus.externalId) {
                const key = PlayerBonusService.recentBonusesHashKey;
                const field = this.getRecentPlayerBonusField(
                    entity.id,
                    playerBonus.externalId
                );
                multi.hset(
                    key,
                    field,
                    JSON.stringify({ id: bonusId, ...playerBonus, createdAt })
                );
                multi.call("HEXPIRE", key, config.playerBonuses.recentPlayerBonusesTTL, "FIELDS", 1, field);
            }
            await multi.exec();
            const event: PendingPlayerBonusEvent = {
                type: PendingPlayerBonusEventType.Create,
                data: {
                    id: bonusId,
                    ...playerBonus,
                    createdAt
                },
                ts: new Date().toISOString()
            };
            await this.sendEventsToKafka([JSON.stringify(event)]);
        });

        return {
            id: bonusId,
            createdAt,
            ...playerBonus
        };
    }

    public async getPlayerBonus(
        entityId: number,
        playerCode: string,
        playerBonusType: PlayerBonusType,
        bonusId: string
    ): Promise<PlayerBonus | undefined> {
        return usingBonusDb<PlayerBonus>(async (db) => {
            const key = this.getPlayerBonusKey(entityId, playerCode, playerBonusType);
            const playerBonus = await db.hget(key, bonusId);
            return JSON.parse(playerBonus);
        });
    }

    public async getPlayerBonuses(
        entity: BaseEntity,
        playerCode: string,
        playerBonusType: PlayerBonusType
    ): Promise<PlayerBonus[]> {
        return usingBonusDb<PlayerBonus[]>(async (db) => {
            const key = this.getPlayerBonusKey(entity.id, playerCode, playerBonusType);
            const playerBonuses = await db.hgetall(key);
            const parsedPlayerBonuses: PlayerBonus[] = [];
            for (const [bonusId, playerBonus] of Object.entries(playerBonuses)) {
                const parsedPlayerBonus: PlayerBonus = JSON.parse(playerBonus);
                parsedPlayerBonuses.push({
                    id: bonusId,
                    ...parsedPlayerBonus
                });
            }
            return parsedPlayerBonuses;
        });
    }

    public async removePlayerBonus(
        entity: BrandEntity,
        playerCode: string,
        playerBonusType: PlayerBonusType,
        bonusId: string,
        currency?: string
    ): Promise<void> {
        const entityId = entity.id;
        await usingBonusDb(async (db) => {
            if (currency) {
                const { useRemoteWallet } = await getEntitySettings(entity.path);
                await this.removeFreeBets(entity, playerCode, bonusId, currency, useRemoteWallet);
            }
            const key = this.getPlayerBonusKey(entityId, playerCode, playerBonusType);
            const playerBonus = await db.hget(key, bonusId);
            if (playerBonus) {
                const parsedPlayerBonus = JSON.parse(playerBonus);
                const deletedCount = await db.hdel(key, bonusId);
                if (deletedCount > 0) {
                    const event: PendingPlayerBonusEvent = {
                        type: PendingPlayerBonusEventType.Cancel,
                        data: {
                            id: bonusId,
                            ...parsedPlayerBonus
                        },
                        ts: new Date().toISOString()
                    };
                    await this.sendEventsToKafka([JSON.stringify(event)]);
                }
            }
        });
    }

    public async removePlayerBonuses(
        entity: BrandEntity,
        playerCode: string,
        playerBonusType: PlayerBonusType,
        currency?: string
    ): Promise<void> {
        await usingBonusDb(async (db) => {
            if (currency) {
                const { useRemoteWallet } = await getEntitySettings(entity.path);
                const playerWallet = new PlayerWalletImpl(entity.id, playerCode, currency);
                const freeBetWallet = new PlayerFreebetWallet(
                    await playerWallet.getWallet(undefined, useRemoteWallet),
                    currency,
                    true
                );
                const freeBetData = freeBetWallet.freeBets;
                if (freeBetData?.length) {
                    const filtered = freeBetData.filter(fb => "isIndividualBonus" in fb && fb.isIndividualBonus) as BonusFreebetsData[];
                    for (const freeBet of filtered) {
                        await this.removeFreeBets(entity, playerCode, freeBet.promoId, currency, useRemoteWallet);
                    }
                }
            }

            const key = this.getPlayerBonusKey(entity.id, playerCode, playerBonusType);
            const playerBonuses = await db.hgetall(key);

            if (playerBonuses) {
                const events: PendingPlayerBonusEvent[] = [];
                for (const [bonusId, playerBonus] of Object.entries(playerBonuses)) {
                    const parsedPlayerBonus = JSON.parse(playerBonus);
                    const deletedCount = await db.hdel(key, bonusId);
                    if (deletedCount > 0) {
                        events.push({
                            type: PendingPlayerBonusEventType.Cancel,
                            data: {
                                id: bonusId,
                                ...parsedPlayerBonus
                            },
                            ts: new Date().toISOString()
                        });
                    }
                }
                if (events.length > 0) {
                    await this.sendEventsToKafka(events.map(event => JSON.stringify(event)));
                }
            }
        });
    }

    private async removeFreeBets(
        entity: BrandEntity,
        playerCode: string,
        bonusId: string,
        currency: string,
        useRemoteWallet?: boolean
    ) {
        const walletFacade = useRemoteWallet ? RemoteWalletFacade : WalletFacade;
        const playerWallet = new PlayerWalletImpl(entity.id, playerCode, currency);
        const trxId = await walletFacade.generateTrxId();
        const trx = await walletFacade.startTransactionWithID(trxId, {
            operationId: OPERATION_ID.FREE_BET,
            operationName: WALLET_OPERATION_NAME.FREE_BET,
            params: {
                promoId: bonusId,
                isIndividualBonus: true
            }
        });
        const freeBetWallet = new PlayerFreebetWallet(await playerWallet.getWallet(trx), currency, true);
        const freeBetData = freeBetWallet.getFreebetPromoData(bonusId, bonusId);
        if (freeBetData) {
            freeBetWallet.resetFreebets(freeBetData);
            await trx.commit();
        }
    }

    public async popPlayerBonus(
        entityId: number,
        playerCode: string,
        currency: string,
        gameCode: string,
        playerBonusType: PlayerBonusType = PlayerBonusType.FreeBet
    ): Promise<PlayerBonus | undefined> {
        return usingBonusDb<PlayerBonus>(async (db) => {
            const key = this.getPlayerBonusKey(entityId, playerCode, playerBonusType);
            const bonus = await this.popPlayerBonusProc.exec(db, [key], [currency, gameCode]);
            return bonus ? JSON.parse(bonus) : undefined;
        });
    }

    private getRecentPlayerBonus(entityId: number, externalId: string): Promise<PlayerBonus | undefined> {
        return usingBonusDb(async (db) => {
            const bonus = await db.hget(
                PlayerBonusService.recentBonusesHashKey,
                this.getRecentPlayerBonusField(entityId, externalId)
            );
            return bonus ? JSON.parse(bonus) : undefined;
        });
    }

    private getPlayerBonusKey(
        brandId: number,
        playerCode: string,
        playerBonusType: PlayerBonusType
    ): string {
        return `player:bonus:${brandId}:${playerCode}:${playerBonusType}`;
    }

    private getRecentPlayerBonusField(
        brandId: number,
        externalId: string
    ): string {
        return `${brandId}:${externalId}`;
    }

    private async validatePlayerBonus(entity: BaseEntity, playerBonus: CreatePlayerBonusDto): Promise<void> {
        const { gameCode, coin, currency } = playerBonus;

        const expireAt = new Date(playerBonus.expiresAt);
        if (expireAt.getTime() < Date.now()) {
            throw new ValidationError("The expiresAt date must be in the future");
        }
        if (!Currencies.exists(currency)) {
            throw new CurrencyNotFoundError(currency);
        }
        if (!entity.currencyExists(currency)) {
            throw new ValidationError(`Currency ${currency} is not available for this entity`);
        }
        const swCurrency = Currencies.get(currency);
        const formattedCoinValue = swCurrency.format(coin);
        if (formattedCoinValue <= 0) {
            throw new ValidationError(`Coin value cannot be negative, zero or have more than ${swCurrency.exponent} digits.`);
        }
        if (formattedCoinValue !== coin) {
            throw new ValidationError(
                `Invalid coin value formatting for currency: ${currency}. Expected: ${formattedCoinValue}, got: ${coin}.`
            );
        }

        if (gameCode) {
            if (Array.isArray(gameCode)) {
                await Promise.all(gameCode.map(game => this.validateGame(game)));
            } else {
                await this.validateGame(gameCode);
            }
        }
    }

    private async validateGame(gameCode: string): Promise<void> {
        const gameFeatures = await gameFeaturesCache.find(gameCode);
        if (!gameFeatures.isFreebetSupported) {
            throw new ValidationError(`Free bets are not supported for game ${gameCode}`);
        }
    }

    private async sendEventsToKafka(messages: string[]): Promise<void> {
        if (!this.kafkaWriter) {
            try {
                this.kafkaWriter = await kafka.createWriter(config.playerBonuses.kafka, logger);
            } catch (err) {
                logger.error(err, "Failed to initialize pending player bonus kafka writer");
                throw err;
            }
        }
        return this.kafkaWriter.sendMessages(messages);
    }
}

export default new PlayerBonusService();
