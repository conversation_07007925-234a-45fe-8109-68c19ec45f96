import { Transaction } from "sequelize";
import * as DynamicDomainCache from "../cache/dynamicDomainCache";
import * as DynamicDomainPoolCache from "../cache/dynamicDomainPoolCache";
import EntityCache from "../cache/entity";
import config from "../config";
import { DynamicDomain } from "../entities/domain";
import { BaseEntity, ChildEntity, EntityWithChild, MIGRATION_STATUS } from "../entities/entity";
import { LiveManagerUrl } from "../entities/lobby";
import * as Errors from "../errors";
import { Models } from "../models/models";
import { sequelize as db } from "../storage/db";
import { ApplicationLock, ApplicationLockId } from "../utils/applicationLock";
import { DynamicDomainService, getDynamicDomainService } from "./domain";
import { findOne } from "./entity";
import { EntityDynamicDomainPoolService } from "./entityDynamicDomainPool";
import MigrationService from "./migrationService";

export const getEntityDynamicDomainService = () => new EntityDynamicDomainService(getDynamicDomainService());

class EntityDynamicDomainService {
    constructor(private domainService: DynamicDomainService) {
    }

    public async set(entity: BaseEntity, domainId: number): Promise<DynamicDomain> {
        return this.changeDynamicDomain(entity, domainId);
    }

    public async reset(entity: BaseEntity): Promise<DynamicDomain> {
        await this.changeDynamicDomain(entity, null);
        return this.get(entity);
    }

    public async get(entity: BaseEntity, playerCode?: string): Promise<DynamicDomain> {
        const domainPoolId = entity.inheritedDynamicDomainPoolId;
        if (domainPoolId && playerCode) {
            const domainPool = await DynamicDomainPoolCache.findOne(domainPoolId);
            if (!domainPool) {
                return Promise.reject(new Errors.DomainPoolNotFoundError());
            }
            const entityDynamicDomainService = new EntityDynamicDomainPoolService(entity);
            return entityDynamicDomainService.pickDynamicDomain(playerCode);
        }
        const domainId = entity.inheritedDynamicDomainId;
        if (domainId) {
            const domain = await DynamicDomainCache.findOne(domainId);
            if (!domain) {
                return Promise.reject(new Errors.DomainNotFoundError());
            }

            return domain.toInfo();
        }
    }

    private async getCurrentDomain(entity: BaseEntity, transaction?: Transaction): Promise<DynamicDomain> {
        const domainId = entity.inheritedDynamicDomainId;
        if (domainId) {
            return this.domainService.findOne(domainId, transaction);
        }
    }

    private async changeDynamicDomain(entity: BaseEntity, domainId: number): Promise<DynamicDomain> {
        const updatedEntities: number[] = [];
        const [newDomain, prevDomain] = await db.transaction(async (transaction) => {

            await ApplicationLock.lock(transaction, ApplicationLockId.UPDATE_DYNAMIC_DOMAIN);

            const updatedEntity = await findOne({ id: entity.id }, undefined, false, transaction);
            const currentDomain = await this.getCurrentDomain(updatedEntity);
            const domain = domainId ? await this.domainService.findOne(
                domainId, transaction, transaction.LOCK.SHARE) : undefined;

            if (updatedEntity.prevDynamicDomainId) { // we have not completed migration
                return Promise.reject(new Errors.MigrationIsInProgressError());
            } else if (updatedEntity.dynamicDomainId === domainId) { // the domain is the same, do nothing;
                return [domain, currentDomain];
            }

            const currentDynamicDomainId = currentDomain?.id || null;
            const currentEnvironment = currentDomain?.environment || null;
            const environment = domain?.environment || null;

            // check environment is not changed
            // and if we reset domain: check that parent has the same domain
            if ((currentEnvironment === environment) ||
                (!domain && this.getParentEnvironment(updatedEntity) === currentEnvironment)) {
                await Models.EntityModel.update(
                    {
                        dynamicDomainId: domainId,
                        environment,
                    } as any,
                    { where: { id: updatedEntity.id }, transaction }
                );
            } else if (updatedEntity.isBrand()) { // set up dynamic domain for brand
                await Models.EntityModel.update(
                    {
                        dynamicDomainId: domainId,
                        environment: environment,
                        prevDynamicDomainId: currentDynamicDomainId,
                        migrationStatus: MIGRATION_STATUS.STARTED,
                    } as any,
                    { where: { id: updatedEntity.id }, transaction }
                );

                updatedEntities.push(updatedEntity.id);
                entity.prevDynamicDomainId = currentDynamicDomainId;
            } else { // set up dynamic domain for entity subtree
                await Models.EntityModel.update(
                    {
                        dynamicDomainId: domainId,
                        environment: environment,
                    } as any,
                    { where: { id: updatedEntity.id }, transaction }
                );

                await this.markChildrenForProgressingMigration(
                    updatedEntity, currentDynamicDomainId, updatedEntities, transaction);
            }

            entity.dynamicDomainId = domainId;
            entity.environment = environment;

            return [domain, currentDomain];
        });
        EntityCache.reset();
        if (prevDomain) {
            updatedEntities.forEach(id => MigrationService.startMigration(id, [prevDomain]));
        }

        return newDomain;
    }

    private getParentEnvironment(baseEntity: BaseEntity) {
        const entity = baseEntity as ChildEntity;
        if (entity.getParent) {
            const parent = entity.getParent();
            return parent && parent.inheritedEnvironment;
        }
        return null;
    }

    private async markChildrenForProgressingMigration(entity: BaseEntity,
                                                      prevDynamicDomainId: number,
                                                      updatedEntities: number[],
                                                      transaction: Transaction): Promise<void> {

        const entityWithChild = entity as EntityWithChild;
        if (entityWithChild.child) {
            for (const child of entityWithChild.child) {
                if (child.prevDynamicDomainId) {
                    return Promise.reject(new Errors.MigrationIsInProgressError());
                }
                if (!child.dynamicDomainId) {
                    if (child.isBrand()) {
                        await Models.EntityModel.update({
                                prevDynamicDomainId,
                                migrationStatus: MIGRATION_STATUS.STARTED,
                            } as any,
                            { where: { id: child.id }, transaction });
                        updatedEntities.push(child.id);
                    } else {
                        await this.markChildrenForProgressingMigration(
                            child, prevDynamicDomainId, updatedEntities, transaction);
                    }
                }
            }
        }
    }
}

export async function validateEntityEnvironment(entity: BaseEntity, environment: string): Promise<void> {
    if (environment) {
        let e = entity;
        while (!e.isMaster() && !e.dynamicDomainId) {
            e = (e as ChildEntity).getParent();
        }
        if (e.environment !== environment && !entity.inheritedDynamicDomainPoolId) {
            return Promise.reject(new Errors.EntityEnvIdChangedError());
        }
    }
}

export async function buildDynamicGSUrl(entity: BaseEntity, endpoint: string, playerCode?: string): Promise<string> {
    const gsServerDomainEntity = await getEntityDynamicDomainService().get(entity, playerCode);
    if (!gsServerDomainEntity) {
        return Promise.reject(new Errors.DynamicDomainNotDefined());
    }

    return buildDynamicGSUrlByDomain(gsServerDomainEntity, endpoint);
}

export async function buildDynamicGSUrlByDomain(dynamicDomain: DynamicDomain, endpoint: string): Promise<string> {
    const schema = config.gameHistory.gameServerSchema;
    const port = config.gameHistory.gameServerPort;

    return `${schema}://${getDomain(dynamicDomain)}:${port}/${endpoint}`;
}

export async function buildDynamicLiveManagerUrl(entity: BaseEntity,
                                                 socketVersion?: string): Promise<LiveManagerUrl | undefined> {
    const domain = await getEntityDynamicDomainService().get(entity);
    if (!domain) {
        return ;
    }

    const { schema, port, path } = config.liveManager;
    let suffix = `:${port}`;
    // strip off default https port if present
    if (schema === "https" && port === 443) {
        suffix = "";
    }
    // strip off default http port if present
    if (schema === "http" && port === 80) {
        suffix = "";
    }

    return {
        url: `${schema}://${getDomain(domain)}${suffix}`,
        path,
        socketPath: config.socket?.[socketVersion?.toLowerCase()]?.path || ""
    };
}

function getDomain(dynamicDomain: DynamicDomain) {
    const gsServerDomain = dynamicDomain.domain.startsWith("http") ?
        dynamicDomain.domain.split("://")[1] :
        dynamicDomain.domain; // strip off schema if present

    return gsServerDomain.split(":")[0]; // strip off port if present
}
