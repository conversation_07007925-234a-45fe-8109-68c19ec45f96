import { kafka, lazy, logging } from "@skywind-group/sw-utils";
import config from "../config";

export interface DomainAnalyticsData {
    readonly type: string;
    readonly ts: number;
    readonly brandId: number;
    readonly playerCode: string;
    readonly gameCode: string;
    readonly initiator: "game-get-url";
    readonly staticDomain: string;
    readonly dynamicDomain: string;
    readonly ehubDomain?: string;
    readonly lobbyDomain?: string;
}

export class DomainAnalyticsService {
    private kafkaWriter: kafka.KafkaWriter;
    private readonly logger = logging.logger("analytics:domains");

    public async send(data: DomainAnalyticsData): Promise<void> {
        if (config.analytics.on && config.analytics.domains.on) {
            try {
                if (!this.kafkaWriter) {
                    this.kafkaWriter = await kafka.createWriter(config.analytics.kafka, this.logger);
                }
                this.logger.debug({ data }, "Sending domain analytics");
                return await this.kafkaWriter.sendMessages([JSON.stringify(data)]);
            } catch (err) {
                this.logger.error(err, "Error sending domain analytics");
            }
        }
    }
}

const service = lazy(() => new DomainAnalyticsService());

export function sendDomainAnalytics(data: DomainAnalyticsData): Promise<void> {
    return service.get().send(data);
}
