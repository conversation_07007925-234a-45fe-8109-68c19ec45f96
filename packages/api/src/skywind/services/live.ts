import * as superagent from "superagent";
import config from "../config";
import { generateTerminalToken } from "../utils/token";
import logger from "../utils/logger";
import * as Errors from "../errors";
import { EntityGameInfo, LiveGameInfo } from "../entities/game";
import { TableInfo } from "@skywind-group/sw-live-core";
import { measure } from "../utils/measures";
import { LiveManagerUrl } from "../entities/lobby";
import { GAME_TYPES, X_TERMINAL_TOKEN } from "../utils/common";

const log = logger("live-manager");

interface LoadParams {
    baseUrl?: string;
    headers?: Record<string, string>;
    qs?: Record<string, string>;
    returnFullResponse?: boolean;
}

class LiveManagerAPI {

    private static readonly TABLES_URL = "/v1/tables";
    private static readonly TABLES_URL_V2 = "/v2/tables";

    @measure({ name: "LiveManagerAPI.getTablesV2", isAsync: true })
    public async getTablesV2(baseUrl: string): Promise<TableInfo[]> {
        const token = await generateTerminalToken({});
        return this.load<TableInfo[]>(LiveManagerAPI.TABLES_URL_V2, {
            baseUrl,
            headers: {
                "Content-Type": "application/json",
                [X_TERMINAL_TOKEN]: token,
            }
        });
    }

    @measure({ name: "LiveManagerAPI.getTables", isAsync: true })
    public async getTables(baseUrl: string, relativeUrl = "/v1"): Promise<Map<string, LiveGameInfo>> {
        const tables: LiveGameInfo[] = await this.get<LiveGameInfo[]>(LiveManagerAPI.TABLES_URL, { baseUrl });
        const physicalTables = new Map<string, LiveGameInfo>();
        for (const table of tables) {
            table.scoreboard = this.getScoreboardUrl(table.provider.toString(), table.id, relativeUrl);
            physicalTables.set(this.getKey(table), table);
        }
        return physicalTables;
    }

    @measure({ name: "LiveManagerAPI.getProviderTables", isAsync: true })
    public async getProviderTables(baseUrl: string, provider: string, relativeUrl = "/v1"): Promise<LiveGameInfo[]> {
        const info = await this.get<LiveGameInfo[]>(`${LiveManagerAPI.TABLES_URL}/${provider}`, { baseUrl });
        info.forEach((game) => game.scoreboard = this.getScoreboardUrl(game.provider.toString(), game.id, relativeUrl));
        return info;
    }

    @measure({ name: "LiveManagerAPI.getTable", isAsync: true })
    public async getTable(baseUrl: string,
                          provider: string,
                          tableId: string,
                          relativeUrl = "/v1"): Promise<LiveGameInfo> {
        const info = await this.get<LiveGameInfo>(`${LiveManagerAPI.TABLES_URL}/${provider}/${tableId}`, { baseUrl });
        if (info) {
            info.scoreboard = this.getScoreboardUrl(provider, tableId, relativeUrl);
        }
        return info;
    }

    protected getKey(table: LiveGameInfo): string {
        return `${table.provider.toString()}:${table.id}`;
    }

    private async get<T>(url: string, { baseUrl, returnFullResponse }: LoadParams = {}): Promise<T> {
        const token = await generateTerminalToken({});
        return this.load<T>(url, { qs: { token }, baseUrl, returnFullResponse });
    }

    private async load<T>(url: string, {
        baseUrl,
        headers,
        qs,
        returnFullResponse
    }: LoadParams = {}): Promise<T> {
        try {
            const requestUrl = `${baseUrl || config.liveManager.baseUrl}${url}`;
            const req = superagent.get(requestUrl).accept("json").query(qs);
            if (headers) {
                req.set(headers);
            }
            const response = await req;

            log.debug({
                baseUrl,
                url,
                requestMethod: response.request.method,
                requestUrl: response.request.url,
                requestBody: response.body,
                responseStatus: response.status,
            }, "Live manager API request/response");

            if (returnFullResponse) {
                return response as any;
            }
            return response.body;
        } catch (err) {
            const logInfo = {
                baseUrl,
                url,
                requestMethod: err.response?.request.method,
                requestUrl: err.response?.request.url,
                responseStatus: err.response?.status,
            };
            log.error({ ...logInfo, err }, "Failed to query live manager");
            throw new Errors.LiveManagerError(err.response?.body);
        }
    }

    private getScoreboardUrl(provider: string, tableId: string, relativeUrl: string): string {
        return `${relativeUrl}/games/live/${provider}/${tableId}/scoreboard.svg`;
    }
}

export const liveManager = new LiveManagerAPI();

export class LiveManagerService {
    public static async getLiveTables(baseUrl: string) {
        return liveManager.getTablesV2(baseUrl);
    }

    public static async getLiveInfoForProviderTables(urlManager: LiveManagerUrl,
                                                     provider: string,
                                                     tableIdsSeparatedWithComma: string,
                                                     relativeUrl: string): Promise<LiveGameInfo[]> {
        const baseUrl = getBaseUrl(urlManager);
        const liveTables = await liveManager.getProviderTables(baseUrl, provider, relativeUrl);
        // return all provider live tables for empty ids
        if (!tableIdsSeparatedWithComma) {
            return liveTables;
        }

        const tabledIds = new Set(tableIdsSeparatedWithComma.split(","));
        return liveTables.filter(liveTable => tabledIds.has(liveTable.id));
    }

    public static async addLiveInfo(urlManager: LiveManagerUrl, games: EntityGameInfo[], relativeUrl?: string) {
        const liveGames = games.filter((game) => game.type === GAME_TYPES.live);
        if (!liveGames.length) {
            return;
        }
        const baseUrl = getBaseUrl(urlManager);
        const liveTables = await liveManager.getTables(baseUrl, relativeUrl);
        for (const game of liveGames) {
            const { provider, tableId } = game.features.live;
            game.live = liveTables.get(`${provider}:${tableId}`);
        }
    }

    public static async addLiveInfoToGame(urlManager: LiveManagerUrl, game: EntityGameInfo, relativeUrl?: string) {
        if (game.type !== GAME_TYPES.live) {
            return;
        }
        const baseUrl = getBaseUrl(urlManager);
        game.live = await liveManager.getTable(
            baseUrl,
            game.features.live.provider,
            game.features.live.tableId,
            relativeUrl
        );
    }
}

function getBaseUrl(liveManagerObj: LiveManagerUrl): string | undefined {
    if (!liveManagerObj) {
        return;
    }

    const { url, path } = liveManagerObj;
    return path ? `${url}/${path}` : url;
}
