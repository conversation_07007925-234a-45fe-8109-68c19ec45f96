import { AccessTokenInfo, User } from "../entities/user";
import * as TokenUtils from "../utils/token";
import { BaseEntity } from "../entities/entity";
import EntityCache from "../cache/entity";
import * as Errors from "../errors";
import { token, lazy } from "@skywind-group/sw-utils";
import * as RolesCache from "../cache/role";
import { getPermissions } from "./role";
import { KeyEntityHolder, PermissionsHolder, SessionHolder, UserInfoHolder } from "./security";
import config from "../config";
import { SUPER_ADMIN_ROLE_ID } from "../utils/common";
import { decodeId, encodeId } from "../utils/publicid";

/**
 * Define a strategy to generate/verify Access token
 */
export interface AccessTokenService {
    /**
     *  Generate access token
     */
    generate(accessTokenInfo: AccessTokenInfo, user: User): Promise<string>;

    /**
     * Verify access token and returns information about user
     */
    verify(token: string): Promise<KeyEntityHolder & PermissionsHolder & UserInfoHolder & SessionHolder>;
}

/**
 * Generate access token with long permissions list inside
 */
export class PermissionsAccessTokenService implements AccessTokenService {
    public async generate(accessTokenInfo: AccessTokenInfo, user: User): Promise<string> {
        const accessTokenData: TokenUtils.AccessTokenData = {
            userId: accessTokenInfo.userId,
            entityId: accessTokenInfo.entityId,
            username: accessTokenInfo.username,
            grantedPermissions: user.grantedPermissions,
            sessionId: accessTokenInfo.sessionId
        };
        if (accessTokenInfo.isSuperAdmin) {
            accessTokenData.isSuperAdmin = accessTokenInfo.isSuperAdmin;
        }

        return TokenUtils.generateAccessToken(accessTokenData);
    }

    public async verify(accessToken: string)
        : Promise<KeyEntityHolder & PermissionsHolder & UserInfoHolder & SessionHolder> {
        try {
            const decoded: TokenUtils.AccessTokenData = await TokenUtils.verifyAccessToken(accessToken);
            const entity: BaseEntity = await EntityCache.findById(decoded.entityId);
            if (!entity) {
                return Promise.reject(new Errors.EntityCouldNotBeFound());
            }
            return {
                keyEntity: entity,
                permissions: {
                    grantedPermissions: decoded.grantedPermissions
                },
                userId: decoded.userId,
                username: decoded.username,
                sessionId: decoded.sessionId
            };
        } catch (err) {
            // catch unique error
            if (err instanceof token.TokenVerifyException) {
                return Promise.reject(new Errors.AccessTokenError());
            }
            if (err instanceof token.TokenExpiredException) {
                return Promise.reject(new Errors.AccessTokenExpired());
            }
            return Promise.reject(err);
        }
    }
}

/**
 * Generate access token with roles list inside
 */
export class RolesAccessTokenService implements AccessTokenService {
    public async generate(accessTokenInfo: AccessTokenInfo, user: User): Promise<string> {
        const accessTokenData: TokenUtils.AccessTokenData = {
            userId: accessTokenInfo.userId,
            entityId: accessTokenInfo.entityId,
            username: accessTokenInfo.username,
            roles: (user.roles || [user.role]).map(r => encodeId(r.id)),
            sessionId: accessTokenInfo.sessionId
        };
        if (accessTokenInfo.isSuperAdmin) {
            accessTokenData.isSuperAdmin = accessTokenInfo.isSuperAdmin;
        }

        return TokenUtils.generateAccessToken(accessTokenData);
    }

    public async verify(accessToken: string)
        : Promise<KeyEntityHolder & PermissionsHolder & UserInfoHolder & SessionHolder> {
        try {
            const decoded: TokenUtils.AccessTokenData = await TokenUtils.verifyAccessToken(accessToken);
            const entity: BaseEntity = await EntityCache.findById(decoded.entityId);
            if (!entity) {
                return Promise.reject(new Errors.EntityCouldNotBeFound());
            }
            let permissions = decoded.grantedPermissions;
            let isSuperAdmin = false;
            if (decoded.roles) {
                decoded.roles = decoded.roles.map(r => decodeId(r));
                const roles = await RolesCache.get(...decoded.roles);
                permissions = getPermissions(roles);

                isSuperAdmin = roles.some(role => role.id === SUPER_ADMIN_ROLE_ID ||
                    role.id === config.security.superAdminRoleId);
            }

            return {
                keyEntity: entity,
                permissions: {
                    grantedPermissions: permissions
                },
                userId: decoded.userId,
                username: decoded.username,
                sessionId: decoded.sessionId,
                isSuperAdmin
            };
        } catch (err) {
            // catch unique error
            if (err instanceof token.TokenVerifyException) {
                return Promise.reject(new Errors.AccessTokenError());
            }
            if (err instanceof token.TokenExpiredException) {
                return Promise.reject(new Errors.AccessTokenExpired());
            }
            return Promise.reject(err);
        }
    }
}

export default lazy(() => {
    if (config.accessToken.type === "permissions") {
        return new PermissionsAccessTokenService();
    } else if (config.accessToken.type === "roles") {
        return new RolesAccessTokenService();
    } else {
        throw new Error("Unknown 'accessToken.type' = config.accessToken.type");
    }
});
