import * as Errors from "../errors";
import { EntityCurrencyRestrictions, EntitySettings } from "../entities/settings";
import { measures } from "@skywind-group/sw-utils";
import { BaseEntity } from "../entities/entity";
import { getEntityJurisdictionService } from "../services/entityJurisdiction";
import { EntityGame } from "../entities/game";
import { CountrySource } from "./countrySource";
import { Jurisdiction } from "../entities/jurisdiction";

interface CountryRestrictions {
    source: "jurisdiction" | "entity";
    allowedCountries: string[];
    restrictedCountries: string[];
}

export async function validateGameCountryRestrictions(entityGame: EntityGame,
                                                      entity: BaseEntity,
                                                      entitySettings: EntitySettings,
                                                      currencyCode: string,
                                                      countrySource: CountrySource,
                                                      jurisdiction?: Jurisdiction): Promise<void> {
    if (!countrySource.code) {
        return;
    }
    validateCurrencyRestrictions(entitySettings.restrictions, currencyCode, countrySource.code, countrySource.source);
    await validateCountryRestrictions(entity, entitySettings, countrySource, jurisdiction);

    const gameCountries = entityGame.game.countries;
    if (gameCountries && gameCountries.length && !gameCountries.includes(countrySource.code)) {
        throw new Errors.CountryIsRestricted(countrySource.code, countrySource.source, "game.countries");
    }

    const countries = entityGame?.settings?.countries;
    if (countries && countries.length) {
        const isBlackList = countries.some(c => c.startsWith("!"));
        if (isBlackList) {
            const countriesBlackList = countries.map(c => c.substring(1));
            if (countriesBlackList.includes(countrySource.code)) {
                throw new Errors.CountryIsRestricted(countrySource.code, countrySource.source, "entityGame.settings.restrictedCountries");
            }
        } else {
            if (!countries.includes(countrySource.code)) {
                throw new Errors.CountryIsRestricted(countrySource.code, countrySource.source, "entityGame.settings.allowedCountries");
            }
        }
    }
}

export async function validatePlayerCountryRestrictions(entity: BaseEntity,
                                                        entitySettings: EntitySettings,
                                                        currencyCode: string,
                                                        countrySource: CountrySource): Promise<void> {
    if (!countrySource.code) {
        return;
    }
    validateCurrencyRestrictions(entitySettings.restrictions, currencyCode, countrySource.code, countrySource.source);
    await validateCountryRestrictions(entity, entitySettings, countrySource);
}

function validateCurrencyRestrictions(restrictions: EntityCurrencyRestrictions | undefined, currencyCode: string,
                                      countryCode: string, countrySource: string): void {
    if (restrictions && restrictions.ignore !== true && restrictions.countries) {
        const allowedCurrencies = restrictions.countries[countryCode] || [];
        if (allowedCurrencies.length && !allowedCurrencies.includes(currencyCode)) {
            throw new Errors.CurrencyIsRestricted(currencyCode, countryCode, countrySource);
        }
    }
}

async function validateCountryRestrictions(entity: BaseEntity,
                                           entitySettings: EntitySettings,
                                           countrySource: CountrySource,
                                           jurisdiction?: Jurisdiction): Promise<void> {
    if (countrySource.restricted) {
        throw new Errors.CountryIsRestricted(countrySource.code, countrySource.source, countrySource.reason);
    }
    const { allowedCountries, restrictedCountries, source } = await getCountryRestrictions(entity, entitySettings, jurisdiction);
    if (!(allowedCountries.length || restrictedCountries.length)) {
        const error = new Errors.ValidationError(`Allowed or restricted countries should be defined [${source}]`);
        error.setExtraData({
            traceId: measures.measureProvider.getTraceID()
        });
        throw error;
    }
    if (allowedCountries.length && !allowedCountries.includes(countrySource.code)) {
        throw new Errors.CountryIsRestricted(
            countrySource.code,
            countrySource.source,
            source === "jurisdiction" ? "jurisdiction.allowedCountries" : "entity.countries"
        );
    }
    if (restrictedCountries.length && restrictedCountries.includes(countrySource.code)) {
        throw new Errors.CountryIsRestricted(
            countrySource.code,
            countrySource.source,
            source === "jurisdiction" ? "jurisdiction.restrictedCountries" : "entitySettings.restrictedCountries"
        );
    }
}

async function getCountryRestrictions(entity: BaseEntity,
                                      entitySettings: EntitySettings,
                                      jurisdiction?: Jurisdiction): Promise<CountryRestrictions> {
    if (entitySettings.useCountriesFromJurisdiction) {
        if (!jurisdiction) {
            [jurisdiction] = await getEntityJurisdictionService().findAll({ entityId: entity.id });
        }
        return {
            source: "jurisdiction",
            allowedCountries: jurisdiction?.allowedCountries || [],
            restrictedCountries: jurisdiction?.restrictedCountries || []
        };
    }
    const countries = entity.getCountries();
    return {
        source: "entity",
        allowedCountries: countries.length && countries || [],
        restrictedCountries: entitySettings.restrictedCountries || []
    };
}
