import { logging } from "@skywind-group/sw-utils";
import Logger = logging.Logger;

export type LoggerFactory = (name?: string) => Logger;
export type ILogger = logging.Logger;

if (logging.getRootLogger() === "") {
    logging.setRootLogger("sw-management-api");
}

export function setRootLogger(name: string) {
    logging.setRootLogger(name);
}

function loggerFactory(name: string = "") {
    return logging.logger(name);
}

export default loggerFactory as LoggerFactory;
