import { AccessStatus } from "../services/domainWatcher/types";

export enum DomainStatus {
    ACTIVE = "active",
    SUSPENDED = "suspended"
}

export enum StaticDomainType {
    STATIC = "static",
    LOBBY = "lobby",
    LIVE_STREAMING = "live-streaming",
    EHUB = "ehub"
}

export interface DomainInfo {
    monitoringStatus?: {
        [adapter: string]: {
            accessStatus: AccessStatus;
            lastCheckedAt?: number;
        }
    }
}

export interface Domain {
    id?: number;
    domain: string;
    description?: string;
    provider?: string;
    status: DomainStatus;
    info?: DomainInfo;
    expiryDate?: Date;
    createdAt?: Date;
    updatedAt?: Date;
}

export type CreateDomain<T extends Domain> = Omit<T, "status"> & Partial<Pick<T, "status">>;

export interface DynamicDomain extends Domain {
    environment: string;
}

export type CreateDynamicDomain = Omit<DynamicDomain, "status"> & Partial<Pick<DynamicDomain, "status">>;

export interface StaticDomain extends Domain {
    type: StaticDomainType;
}

export type CreateStaticDomain = Omit<StaticDomain, "status" | "type"> & Partial<Pick<StaticDomain, "status" | "type">>;
