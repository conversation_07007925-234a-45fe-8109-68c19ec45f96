import { measures } from "@skywind-group/sw-utils";
import { loadDomains } from "../services/domainWatcher/loadDomains";
import { CronJob } from "../utils/cronJob";
import config from "../config";
import { DomainWatcher, getDomainWatcher } from "../services/domainWatcher/domainWatcher";
import logger from "../utils/logger";

const name = "domain-monitoring";
const log = logger(`job:${name}`);

export class DomainMonitoringJob {
    private readonly watchers = new Map<string, DomainWatcher>();

    public async fire() {
        await measures.measureProvider.runInTransaction("Domain monitoring job", async () => {
            try {
                const items = await loadDomains();
                if (items.size) {
                    for (const [adapterId, domains] of items.entries()) {
                        log.info(domains, "Updating domains for adapter %s", adapterId);
                        await this.getWatcher(adapterId).update(domains);
                    }
                } else {
                    log.info("No domains to update");
                }
            } catch (err) {
                measures.measureProvider.saveError(err);
                throw err;
            }
        });
    }

    private getWatcher(adapter: string) {
        let item = this.watchers.get(adapter);
        if (!item) {
            item = getDomainWatcher(adapter, log);
            this.watchers.set(adapter, item);
        }
        return item;
    }
}

const job = new DomainMonitoringJob();

let cronJob: CronJob;

export async function initDomainMonitoringJob(): Promise<void> {
    if (config.domainMonitoring.cronJob.enabled && !cronJob) {
        cronJob = new CronJob({
            name,
            schedule: config.domainMonitoring.cronJob.schedule,
            timeout: config.domainMonitoring.cronJob.timeout
        }, job.fire.bind(job));

        if (config.domainMonitoring.cronJob.runOnServerStart) {
            cronJob.invoke.bind(job);
            await cronJob.invoke();
        }
    }
}
