import { Models } from "../models/models";
import config from "../config";
import { StaticDomainPoolDBInstance } from "../models/staticDomainPool";
import { domainPoolAssociations } from "../services/staticDomainPool";
import { HierarchicalCache } from "./hierarchicalCache";

const StaticDomainPoolModel = Models.StaticDomainPoolModel;

const cache = new HierarchicalCache<string, StaticDomainPoolDBInstance>("static-domain-pool", searchInDb, {
    stdTTL: config.staticDomainPoolCache.ttl,
    checkperiod: config.staticDomainPoolCache.checkPeriod
});

async function searchInDb(id: string): Promise<StaticDomainPoolDBInstance> {
    return StaticDomainPoolModel.findByPk(id, { include: domainPoolAssociations });
}

export function reset(id?: number) {
    cache.reset(id && `${id}`);
}

export async function findOne(id: number): Promise<StaticDomainPoolDBInstance> {
    return cache.find(`${id}`);
}
