import * as redis from "../storage/redis";
import { Redis as RedisClient } from "ioredis";
import config from "../config";
import * as NodeCache from "node-cache";
import { CacheTree } from "./cacheTree";
import { CacheConfig } from "./cache";
import { Key } from "node-cache";

export class HierarchicalCache<ID extends Key, T> {
    protected cache: NodeCache;
    protected cacheTree: CacheTree;
    protected channel: string;
    protected sub: RedisClient;
    protected pub: RedisClient;
    protected connected: boolean = false;
    protected separator: string;

    constructor(
        name: string, 
        protected search: Function,
        cacheConfig?: CacheConfig,
        separator: string = ":"
    ) {
        this.channel = "hierarchical-cache:" + name;
        this.separator = separator;
        this.sub = redis.create();
        this.pub = redis.create();
        this.cacheTree = new CacheTree(separator);
        this.initNodeCache(cacheConfig);
    }

    private initNodeCache(cacheConfig?: CacheConfig) {
        if ((cacheConfig?.stdTTL || 0) >= 0) {
            this.cache = new NodeCache({
                stdTTL: config.cache.ttl,
                checkperiod: config.cache.checkPeriod,
                useClones: false,
                ...cacheConfig
            });

            this.listenInvalidates();
        }
    }

    public async find(id: ID, ...args: any[]): Promise<T> {
        if (this.cache && this.connected) {
            let value = this.cache.get<T>(id);

            if (!value) {
                value = await this.search.call(this.search, id, ...args);
                if (value) {
                    this.cache.set(id, value);
                    this.cacheTree.addKey(String(id));
                } else {
                    this.cache.del(id);
                    this.cacheTree.removeKey(String(id));
                }
            }

            return value;
        } else {
            return this.search.call(this.search, id, ...args);
        }
    }

    public reset(prefix?: string) {
        if (this.cache) {
            if (!prefix) {
                // No prefix - flush all
                this.cache.flushAll();
                this.cacheTree.clear();
                this.notifyHierarchyInvalidated("");
            } else if (prefix === this.separator) {
                // Master entity case (just ":") - flush all
                this.cache.flushAll();
                this.cacheTree.clear();
                this.notifyHierarchyInvalidated(prefix);
            } else {
                // Hierarchical invalidation based on prefix
                const keysToDelete = this.cacheTree.getKeysWithPrefix(prefix);
                
                keysToDelete.forEach(key => {
                    this.cache.del(key);
                });
                
                keysToDelete.forEach(key => {
                    this.cacheTree.removeKey(key);
                });
                
                this.notifyHierarchyInvalidated(prefix);
            }
        }
    }

    private notifyHierarchyInvalidated(prefix: string) {
        this.pub.publish(this.channel, JSON.stringify({ hierarchyPath: prefix }));
    }

    public keys(): string[] {
        return this.cache ? this.cache.keys() : [];
    }

    private listenInvalidates() {
        this.sub.subscribe(this.channel);
        this.sub.on("message", (channel, message) => {
            if (this.channel === channel) {
                const msg = JSON.parse(message);
                if (msg.hierarchyPath) {
                    if (msg.hierarchyPath === this.separator) {
                        this.cache.flushAll();
                        this.cacheTree.clear();
                    } else {
                        const keysToDelete = this.cacheTree.getKeysWithPrefix(msg.hierarchyPath);
                        for (const key of keysToDelete) {
                            this.cache.del(key);
                            this.cacheTree.removeKey(key);
                        }
                    }
                } else if (msg.id) {
                    this.cache.del(msg.id);
                    this.cacheTree.removeKey(msg.id);
                } else {
                    this.cache.flushAll();
                    this.cacheTree.clear();
                }
            }
        });

        this.sub.on("error", () => {
            if (this.cache && this.connected) {
                this.cache.flushAll();
                this.cacheTree.clear();
            }
            this.connected = false;
        });

        this.sub.on("connect", () => {
            this.connected = true;
        });
    }
}
