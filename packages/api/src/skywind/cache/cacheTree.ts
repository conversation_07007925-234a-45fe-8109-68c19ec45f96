interface CacheTreeNode {
    children: Map<string, CacheTreeNode>;
    keys: Set<string>;
}

export class CacheTree {
    private root: CacheTreeNode;
    private readonly separator: string;

    constructor(separator: string = ":") {
        this.separator = separator;
        this.root = {
            children: new Map(),
            keys: new Set()
        };
    }

    public addKey(key: string): void {
        const pathSegments = key.split(this.separator);
        let currentNode = this.root;

        for (const segment of pathSegments) {
            currentNode.keys.add(key);
            
            if (!currentNode.children.has(segment)) {
                currentNode.children.set(segment, {
                    children: new Map(),
                    keys: new Set()
                });
            }
            
            currentNode = currentNode.children.get(segment);
        }
        
        currentNode.keys.add(key);
    }

    public removeKey(key: string): void {
        const pathSegments = key.split(this.separator);
        const nodePath: CacheTreeNode[] = [this.root];
        let currentNode = this.root;

        for (const segment of pathSegments) {
            if (!currentNode.children.has(segment)) {
                return;
            }
            currentNode = currentNode.children.get(segment);
            nodePath.push(currentNode);
        }

        for (const node of nodePath) {
            node.keys.delete(key);
        }
    }

    public getKeysWithPrefix(prefix: string): string[] {
        // Handle empty prefix or master entity case (just ":")
        if (!prefix || prefix === this.separator) {
            return Array.from(this.root.keys);
        }

        // Split and filter out empty segments to handle cases like ":" or ":entity"
        const pathSegments = prefix.split(this.separator).filter(segment => segment !== "");
        let currentNode = this.root;

        for (const segment of pathSegments) {
            if (!currentNode.children.has(segment)) {
                return [];
            }
            currentNode = currentNode.children.get(segment);
        }

        return Array.from(currentNode.keys);
    }

    public clear(): void {
        this.root = {
            children: new Map(),
            keys: new Set()
        };
    }
}
