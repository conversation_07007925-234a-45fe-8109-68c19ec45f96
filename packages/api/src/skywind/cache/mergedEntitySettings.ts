import { EntitySettings } from "../entities/settings";
import config from "../config";
import { HierarchicalCache } from "./hierarchicalCache";
import { getMergedEntitySettings } from "../services/settings";

class MergedEntitySettingsCache extends HierarchicalCache<string, EntitySettings> {
    constructor() {
        super(
            "merged-entity-settings-cache",
            function (keys: string[]) {
                return getMergedEntitySettings(keys);
            },
            {
                stdTTL: config.mergedEntitySettingsCache.ttl,
                checkperiod: config.mergedEntitySettingsCache.checkPeriod
            }
        );
    }

    protected retrieve(): Promise<EntitySettings> {
        return Promise.resolve(undefined);
    }

    public async find(path: string, keys: string[]): Promise<EntitySettings> {
        if (this.cache && this.connected) {
            let value: EntitySettings = this.cache.get(path);

            if (!value) {
                value = await this.search.call(this.search, keys);
                if (value?.cacheMergedEntitySettings) {
                    this.cache.set(path, value);
                    this.cacheTree.addKey(path);
                } else if (!value) {
                    this.cache.del(path);
                    this.cacheTree.removeKey(path);
                }
            }

            return value;
        } else {
            return this.search.call(this.search, keys);
        }
    }
}

export default new MergedEntitySettingsCache();
