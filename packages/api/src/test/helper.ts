import { sleep as sleepUtils } from "@skywind-group/sw-utils";
import * as MerchantTypesCache from "../skywind/cache/merchantTypes";
import { getMerchantTypeModel } from "../skywind/models/merchantType";
import { AnyMerchantAdapter, registerAdapter } from "@skywind-group/sw-management-adapters";
import { LIMIT_CONFIGURATION_TYPE } from "../skywind/entities/schemaDefinition";

export const sleep = sleepUtils;

export function convertDateFields(instance, listOfDateFields = ["createdAt", "updatedAt"]) {
    const convertedDates = {};
    for (const field of listOfDateFields) {
        const date = instance[field];
        if (date) {
            convertedDates[field] = (instance[field] as Date).toISOString();
        }
    }
    return {
        ...instance,
        ...convertedDates
    };
}

export function getRandomNumber(min: number = 0, max: number): number {
    return Math.floor(Math.random() * (max - min + 1) + min);
}

export function parseJwt(token: string) {
    const base64Url = token.split(".")[1];
    const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
    const buff = new Buffer(base64, "base64");
    const jsonPayload = buff.toString("ascii");

    return JSON.parse(jsonPayload);
}

export const getConfigWithLevels = (levelId1: string, levelId2: string) => {
    return {
        "CNY": {
            [levelId1]: {
                "stakeAll": [1, 5, 10, 20, 50, 100, 250, 500],
            },
            [levelId2]: {
                "stakeAll": [1, 5, 10, 20, 50, 100, 250, 500],
            }
        },
        "EUR": {
            [levelId1]: {
                "stakeAll": [0.1, 0.5, 1, 2, 5, 10, 25, 50],
                defaultTotalStake: 10
            },
            [levelId2]: {
                "stakeAll": [0.1, 0.5, 1, 2, 5, 10, 25, 50],
                defaultTotalStake: 10
            }
        }
    };
};

let i = 0;
export const getDefinitionWithSets = () => {
    const data = {
        name: `Blackjack_${i}`,
        schema: {
            "type": "object",
            "properties": {
                "stakeAll": {
                    "title": "Stakes",
                    "type": "array",
                    "items": {
                        "type": "number"
                    },
                    "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE // values can be selected
                },
                "defaultTotalStake": {
                    "type": "number",
                    "title": "Default Total Bet",
                    "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
                },
                "stakeDef": {
                    "type": "number",
                    "title": "Default Stake",
                    "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED
                },
                "stakeMax": {
                    "type": "number",
                    "title": "Max Stake",
                    "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED
                },
                "stakeMin": {
                    "type": "number",
                    "title": "Min Stake",
                    "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED
                }
            },
            "levels": true
        },
        permissions: {
            default: {
                "stakeAll": "entity",
                "defaultTotalStake": "entity"
            }
        }
    };
    i++;
    return data;
};

export const getConfigForRoulette = (levelId1, levelId2) => ({
    "CNY": {
        [levelId1]: {
            stakeAll: [1, 5, 10, 20, 50, 100, 250, 500],
            defaultTotalStake: 50,
            bets: {
                straight: { max: 200000, min: 500},
                column_dozen: {max: 200000, min: 1000}
            }
        },
        [levelId2]: {
            stakeAll: [20, 50, 100, 250, 500, 1000],
            defaultTotalStake: 30,
            bets: {
                straight: { max: 20000, min: 50},
                column_dozen: {max: 20000, min: 100}
            }
        },
    },
    "EUR": {
        [levelId1]: {
            stakeAll: [0.1, 0.5, 1, 2, 5, 10, 25, 50],
            defaultTotalStake: 10,
            bets: {
                straight: { max: 20000, min: 50},
                column_dozen: {max: 20000, min: 100}
            },
            totalStakeMax: 3000,
            totalStakeMin: 1
        },
        [levelId2]: {
            stakeAll: [0.1, 0.5, 1, 2, 5, 10, 25, 50],
            defaultTotalStake: 10,
            bets: {
                straight: { max: 2000, min: 5},
                column_dozen: {max: 2000, min: 10}
            },
            totalStakeMax: 2000,
            totalStakeMin: 1
        }
    }
});

export const getRouletteDefinition = () => ({
    "name": "Roulette",
    "definitions": {
        "bet": {
            "type": "object",
            "properties": {
                "max": {
                    "type": "number",
                    "title": "Maximum",
                    "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
                },
                "min": {
                    "type": "number",
                    "title": "Minimum",
                    "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
                },
                "payout": {
                    "type": "number",
                    "title": "Minimum",
                    "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONST
                },
            }
        }
    },
    "schema": {
        "type": "object",
        "properties": {
            "bets": {
                "type": "object",
                "properties": {
                    "straight": {
                        "$ref": "/bet"
                    },
                    "column_dozen": {
                        "$ref": "/bet"
                    }
                }
            },
            "stakeAll": {
                "title": "Stakes",
                "type": "array",
                "items": {
                    "type": "number"
                },
                "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
            },
            "defaultTotalStake": {
                "type": "number",
                "title": "Default Total Bet",
                "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
            },
            "stakeMax": {
                "type": "number",
                "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED
            },
            "totalStakeMax": {
                "type": "number",
                "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
            },
            "stakeMin": {
                "type": "number",
                "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED
            },
            "totalStakeMin": {
                "type": "number",
                "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
            }
        },
        "levels": true
    }
});

export const getRouletteExposureDefinition = () => ({
    "name": "Roullete with exposure",
    "definitions": {
        "bet": {
            "type": "object",
            "properties": {
                "max": {
                    "type": "number",
                    "title": "Maximum",
                    "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
                },
                "min": {
                    "type": "number",
                    "title": "Minimum",
                    "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
                },
                "exposure": {
                    "type": "number",
                    "title": "Bet exposure",
                    "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED
                },
                "payout": {
                    "type": "number",
                    "title": "Minimum",
                    "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONST
                },
            }
        }
    },
    "schema": {
        "type": "object",
        "properties": {
            "bets": {
                "type": "object",
                "properties": {
                    "straight": {
                        "$ref": "/bet"
                    },
                    "column_dozen": {
                        "$ref": "/bet"
                    },
                    "street": {
                        "$ref": "/bet"
                    },
                    "split": {
                        "$ref": "/bet"
                    },
                    "corner": {
                        "$ref": "/bet"
                    },
                    "line": {
                        "$ref": "/bet"
                    },
                    "color_parity_high": {
                        "$ref": "/bet"
                    }
                }
            },
            "exposure": {
                "type": "number",
                "title": "Bet exposure",
                "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED
            },
            "stakeAll": {
                "title": "Stakes",
                "type": "array",
                "items": {
                    "type": "number"
                },
                "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
            },
            "defaultTotalStake": {
                "type": "number",
                "title": "Default Total Bet",
                "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
            },
            "stakeMax": {
                "type": "number",
                "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED
            },
            "totalStakeMax": {
                "type": "number",
                "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
            },
            "stakeMin": {
                "type": "number",
                "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED
            },
            "totalStakeMin": {
                "type": "number",
                "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
            },
            "stakeDef": {
                "type": "number",
                "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
            }
        },
        "levels": true
    },
    "permissions": {
        "EUR": {
            "bets": "entity",
            "bets.payout": "admin",
            "stakeAll": "entity",
            "totalStakeMax": "entity",
            "totalStakeMin": "entity",
            "stakeDef": "entity"
        },
        "CNY": {
            "bets": "entity",
            "stakeAll": "entity",
            "totalStakeMax": "entity",
            "totalStakeMin": "entity",
            "stakeDef": "entity"
        },
        "BNS": {
            "bets": "entity",
            "stakeAll": "admin",
            "totalStakeMax": "entity",
            "totalStakeMin": "entity",
            "stakeDef": "entity"
        }
    }
});

export const getBaccaratDefinition = () => ({
    "name": "Live test new baccarat",
    "definitions": {
        "bet": {
            "type": "object",
            "properties": {
                "max": {
                    "type": "number",
                    "title": "Maximum",
                    "limitConfigurationType": "configurable"
                },
                "min": {
                    "type": "number",
                    "title": "Minimum",
                    "limitConfigurationType": "configurable"
                },
                "exposure": {
                    "type": "number",
                    "title": "Bet exposure",
                    "limitConfigurationType": "calculated"
                },
                "payout": {
                    "type": "number",
                    "title": "Minimum",
                    "limitConfigurationType": "constForAllCurrencies"
                }
            }
        }
    },
    "schema": {
        "type": "object",
        "properties": {
            "bets": {
                "type": "object",
                "properties":  {
                    "playerPair": {
                        "$ref": "/bet"
                    },
                    "egalite": {
                        "$ref": "/bet"
                    },
                    "big": {
                        "$ref": "/bet"
                    },
                    "bankerPair": {
                        "$ref": "/bet"
                    },
                    "perfectPair": {
                        "$ref": "/bet"
                    },
                    "player": {
                        "$ref": "/bet"
                    },
                    "banker": {
                        "$ref": "/bet"
                    },
                    "anyPair": {
                        "$ref": "/bet"
                    },
                    "small": {
                        "$ref": "/bet"
                    },
                    "tie": {
                        "$ref": "/bet"
                    },
                    "super6": {
                        "$ref": "/bet"
                    }
                }
            },
            "exposure": {
                "type": "number",
                "title": "Bet exposure",
                "limitConfigurationType": "calculated"
            },
            "stakeAll": {
                "title": "Stakes",
                "type": "array",
                "items": {
                    "type": "number"
                },
                "limitConfigurationType": "configurable"
            },
            "defaultTotalStake": {
                "type": "number",
                "title": "Default Total Bet",
                "limitConfigurationType": "configurable"
            },
            "stakeMax": {
                "type": "number",
                "limitConfigurationType": "calculated"
            },
            "stakeDef": {
                "type": "number",
                "limitConfigurationType": "configurable"
            },
            "totalStakeMax": {
                "type": "number",
                "limitConfigurationType": "configurable"
            },
            "stakeMin": {
                "type": "number",
                "limitConfigurationType": "calculated"
            },
            "totalStakeMin": {
                "type": "number",
                "limitConfigurationType": "configurable"
            }
        },
        "levels": true
    },
    "permissions": {
        "EUR": {
            "bets": "entity",
            "bets.payout": "admin",
            "stakeAll": "entity",
            "totalStakeMax": "entity",
            "totalStakeMin": "entity",
            "stakeDef": "entity"
        },
        "CNY": {
            "bets": "entity",
            "stakeAll": "entity",
            "totalStakeMax": "entity",
            "totalStakeMin": "entity",
            "stakeDef": "entity"
        },
        "BNS": {
            "bets": "entity",
            "stakeAll": "admin",
            "totalStakeMax": "entity",
            "totalStakeMin": "entity",
            "stakeDef": "entity"
        }
    }
});

export const fixedStakeAllDefinitionWithFixedType = () => ({
    name: "Slot games with fixed stakeAll ",
    schema: {
        "type": "object",
        "properties": {
            "stakeDef": { "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED, "type": "number" },
            "stakeMax": { "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED, "type": "number" },
            "maxTotalStake": {
                "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED,
                "type": "number"
            },
            "stakeMin": { "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED, "type": "number" },
            "stakeAll": {
                "items": { "type": "number" },
                "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.FIXED,
                "type": "array",
                "title": "Stakes"
            },
            "winMax": {
                "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE,
                "type": "number",
                "title": "Win Capping"
            }
        },
        levels: false,
    }
});

export async function register(type: string, adapter: AnyMerchantAdapter) {
    registerAdapter(type, adapter);
    const existingAdapter = await MerchantTypesCache.findOne(type);
    if (existingAdapter) {
        return;
    }
    try {
        await getMerchantTypeModel().create({ type, schema: {} });
    } finally {
        MerchantTypesCache.reset();
    }
}
