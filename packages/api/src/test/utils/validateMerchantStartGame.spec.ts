import { suite, test } from "mocha-typescript";
import { expect, should, use } from "chai";
import { stub, SinonStub } from "sinon";
import { BrandEntity } from "../../skywind/entities/brand";
import { Merchant } from "../../skywind/entities/merchant";

use(require("chai-as-promised"));
import { getBlockedMerchantPlayer } from "../../skywind/models/merchantBlockedPlayer";
import { validateAndGetPlayerTestStatus, validateMerchantStartGame } from "../../skywind/services/playService";
import { StartGameTokenData } from "@skywind-group/sw-wallet-adapter-core";
import { EntityGame } from "../../skywind/entities/game";
import * as Errors from "../../skywind/errors";
import * as EntityModule from "../../skywind/services/entity";
import * as MerchantTestPlayerCache from "../../skywind/cache/testPlayers";
import { SOURCE } from "../../skywind/entities/merchantTestPlayer";

@suite
class ValidateMerchantStartGameSpec {
    public brandMerchantMock: BrandEntity = { isMerchant: true, status: "test" } as any;
    public merchantMock: Merchant = { brandId: 1 } as any;
    public startGameTokenDataMock: StartGameTokenData = { playmode: "real" } as any;
    public entityGameMock: EntityGame = { status: "test" } as any;
    public static validateEntityBalanceStub: SinonStub;
    public static playerBlockedModuleStub: SinonStub;
    public static merchantTestPlayerServiceStub: SinonStub;

    public static async before() {
        should();
        this.validateEntityBalanceStub = stub(EntityModule, "validateEntityBalance").returns(Promise.resolve());
        const blockedModule = getBlockedMerchantPlayer();
        this.playerBlockedModuleStub = stub(blockedModule, "findOne").returns(undefined);
        this.merchantTestPlayerServiceStub = stub(MerchantTestPlayerCache, "findOne").resolves({
            source: SOURCE.INTEGRATION
        } as any);
    }

    public static async after() {
        this.validateEntityBalanceStub.restore();
        this.playerBlockedModuleStub.restore();
        this.merchantTestPlayerServiceStub.restore();
    }

    @test()
    public async playerBlocked() {
        ValidateMerchantStartGameSpec.playerBlockedModuleStub.returns({ obj: 1 });
        await expect(validateMerchantStartGame(this.brandMerchantMock,
            this.startGameTokenDataMock)).to.be.rejectedWith(Errors.PlayerIsSuspended);
        ValidateMerchantStartGameSpec.playerBlockedModuleStub.returns(undefined);
    }

    @test()
    public async integrationTestPlayerWithoutTestFlag() {
        await expect(validateAndGetPlayerTestStatus(this.brandMerchantMock,
            this.startGameTokenDataMock,
            false, this.entityGameMock))
            .to
            .be
            .rejectedWith(Errors.MerchantTestPlayerError,
                "Player marked as integration test player, but not marked as test by the operator.");
    }

    @test()
    public async integrationTestPlayerWithWrongEndDate() {
        const player = {
            endDate: new Date("2020-11-12")
        };
        ValidateMerchantStartGameSpec.merchantTestPlayerServiceStub.returns(Promise.resolve(player));
        await expect(validateAndGetPlayerTestStatus(this.brandMerchantMock,
            this.startGameTokenDataMock,
            false, this.entityGameMock))
            .to
            .be
            .rejectedWith(Errors.MerchantTestPlayerError,
                `invalid test player end date ${player.endDate}. 
                    End date should > current date`);
        ValidateMerchantStartGameSpec.merchantTestPlayerServiceStub.returns({
                findOne: async () => Promise.resolve({
                    source: SOURCE.INTEGRATION
                })
            }
        );
    }

    @test()
    public async testGameAndNotFoundPlayerWithoutTestFlag() {
        ValidateMerchantStartGameSpec.merchantTestPlayerServiceStub.returns(Promise.resolve(undefined));
        await expect(validateAndGetPlayerTestStatus(this.brandMerchantMock,
            this.startGameTokenDataMock,
            false, this.entityGameMock))
            .to
            .be
            .rejectedWith(Errors.MerchantTestPlayerError,
                "only test player can play in tests game");
        ValidateMerchantStartGameSpec.merchantTestPlayerServiceStub.returns({
                findOne: async () => Promise.resolve({
                    source: SOURCE.INTEGRATION
                })
            }
        );
    }
}
