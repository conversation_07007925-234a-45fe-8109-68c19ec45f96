import { expect, use } from "chai";
import { getEntityStaticDomainService } from "../../skywind/services/entityStaticDomainService";
import { getStaticDomainService } from "../../skywind/services/domain";
import { StaticDomain, StaticDomainType } from "../../skywind/entities/domain";
import { BaseEntity } from "../../skywind/entities/entity";
import * as Errors from "../../skywind/errors";
import { factory } from "factory-girl";
import { FACTORY } from "../factories/common";
import { truncate } from "../entities/helper";

use(require("chai-as-promised"));

describe("EntityStaticDomainService.set with domain type validation", () => {
    
    const service = getEntityStaticDomainService();
    let entity: BaseEntity;
    let lobbyDomain: StaticDomain;
    let ehubDomain: StaticDomain;
    let liveStreamingDomain: StaticDomain;
    let staticDomain: StaticDomain;
    
    before(async () => {
        await truncate();

        entity = await factory.create(FACTORY.BRAND);
        const domainService = getStaticDomainService();

        // Create domains of different types with unique names to avoid conflicts
        const timestamp = Date.now();
        lobbyDomain = await domainService.create({
            domain: `.testlobby${timestamp}.com`,
            type: StaticDomainType.LOBBY
        });
        ehubDomain = await domainService.create({
            domain: `testehub${timestamp}.com`,
            type: StaticDomainType.EHUB
        });
        liveStreamingDomain = await domainService.create({
            domain: `testlive${timestamp}.com`,
            type: StaticDomainType.LIVE_STREAMING
        });
        staticDomain = await domainService.create({
            domain: `teststatic${timestamp}.com`,
            type: StaticDomainType.STATIC
        });
    });

    afterEach(async () => {
        await service.reset(entity, StaticDomainType.LOBBY);
        await service.reset(entity, StaticDomainType.EHUB);
        await service.reset(entity, StaticDomainType.LIVE_STREAMING);
        await service.reset(entity, StaticDomainType.STATIC);
    });

    it("should successfully assign lobby domain to lobby slot", async () => {
        const result = await service.set(entity, lobbyDomain.id, StaticDomainType.LOBBY);
        expect(result).to.deep.equal(lobbyDomain);
        expect(entity.lobbyDomainId).to.equal(lobbyDomain.id);
    });

    it("should successfully assign ehub domain to ehub slot", async () => {
        const result = await service.set(entity, ehubDomain.id, StaticDomainType.EHUB);
        expect(result).to.deep.equal(ehubDomain);
        expect(entity.ehubDomainId).to.equal(ehubDomain.id);
    });

    it("should successfully assign live streaming domain to live streaming slot", async () => {
        const result = await service.set(entity, liveStreamingDomain.id, StaticDomainType.LIVE_STREAMING);
        expect(result).to.deep.equal(liveStreamingDomain);
        expect(entity.liveStreamingDomainId).to.equal(liveStreamingDomain.id);
    });

    it("should successfully assign static domain to static slot", async () => {
        const result = await service.set(entity, staticDomain.id, StaticDomainType.STATIC);
        expect(result).to.deep.equal(staticDomain);
        expect(entity.staticDomainId).to.equal(staticDomain.id);
    });

    it("should reject assigning lobby domain to ehub slot", async () => {
        await expect(service.set(entity, lobbyDomain.id, StaticDomainType.EHUB))
            .to.be.rejectedWith(Errors.ValidationError)
            .and.eventually.have.property("message")
            .that.include("Domain type mismatch: Cannot assign lobby domain to ehub domain slot");
    });

    it("should reject assigning ehub domain to lobby slot", async () => {
        await expect(service.set(entity, ehubDomain.id, StaticDomainType.LOBBY))
            .to.be.rejectedWith(Errors.ValidationError)
            .and.eventually.have.property("message")
            .that.include("Domain type mismatch: Cannot assign ehub domain to lobby domain slot");
    });

    it("should reject assigning static domain to live streaming slot", async () => {
        await expect(service.set(entity, staticDomain.id, StaticDomainType.LIVE_STREAMING))
            .to.be.rejectedWith(Errors.ValidationError)
            .and.eventually.have.property("message")
            .that.include("Domain type mismatch: Cannot assign static domain to live-streaming domain slot");
    });

    it("should reject assigning live streaming domain to static slot", async () => {
        await expect(service.set(entity, liveStreamingDomain.id, StaticDomainType.STATIC))
            .to.be.rejectedWith(Errors.ValidationError)
            .and.eventually.have.property("message")
            .that.include("Domain type mismatch: Cannot assign live-streaming domain to static domain slot");
    });

    it("should not modify entity when domain type validation fails", async () => {
        const originalLobbyDomainId = entity.lobbyDomainId;
        const originalEhubDomainId = entity.ehubDomainId;

        try {
            await service.set(entity, lobbyDomain.id, StaticDomainType.EHUB);
        } catch (error) {
            // Expected to fail
        }

        // Entity should not be modified
        expect(entity.lobbyDomainId).to.equal(originalLobbyDomainId);
        expect(entity.ehubDomainId).to.equal(originalEhubDomainId);
    });
});
