import { expect } from "chai";
import { ChatService } from "../../skywind/services/chatService";
import { GameSettings, Live } from "../../skywind/entities/game";
import { EntitySettingsUpdate } from "../../skywind/entities/settings";

describe("Chat Setting service", () => {
    it("mergeAndBuildChannel", () => {
        let entityGameSettings: GameSettings = {};
        let entitySettings: EntitySettingsUpdate = {};
        let gameSettings: Live = {
            tableId: "1",
            provider: "mock"
        };
        expect(
            ChatService.mergeAndBuildChannel(entityGameSettings, entitySettings, gameSettings)
        ).eq("mock:1");

        entityGameSettings = { social: true };
        expect(
            ChatService.mergeAndBuildChannel(entityGameSettings, entitySettings, gameSettings)
        ).eq("mock:1:social");

        entitySettings = { social: false };
        expect(
            ChatService.mergeAndBuildChannel(entityGameSettings, entitySettings, gameSettings)
        ).eq("mock:1");

        entityGameSettings = { social: true };
        entitySettings = { social: true };
        gameSettings["social"] = false;
        expect(
            ChatService.mergeAndBuildChannel(entityGameSettings, entitySettings, gameSettings)
        ).eq("mock:1");
    });
});
