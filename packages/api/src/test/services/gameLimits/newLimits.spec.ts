import { suite, test, timeout } from "mocha-typescript";
import { createComplexStructure, truncate } from "../../entities/helper";
import {
    GameLimitsConfigurationService,
    getGameLimitsConfigurationService
} from "../../../skywind/services/gameLimits/gameLimitsConfiguration";
import { BaseEntity } from "../../../skywind/entities/entity";
import { CreateGameLimitsConfiguration } from "../../../skywind/models/gameLimitsConfiguration";
import { BrandEntity } from "../../../skywind/entities/brand";
import { getSchemaDefinitionService } from "../../../skywind/services/gameLimits/schemaDefinition";
import { getSchemaConfigurationService } from "../../../skywind/services/gameLimits/schemaConfiguration";
import { SchemaConfiguration } from "../../../skywind/models/schemaConfiguration";
import { EntityGame, Game } from "../../../skywind/entities/game";
import { factory } from "factory-girl";
import { FACTORY } from "../../factories/common";
import { expect } from "chai";
import { CurrencyMultiplier } from "../../../skywind/models/currencyMultiplier";
import * as EntityService from "../../../skywind/services/entity";
import { getCurrencyMultiplierService } from "../../../skywind/services/gameLimits/currencyMultiplier";
import EntitySettingsService from "../../../skywind/services/settings";
import { getNewLimitsFacade } from "../../../skywind/services/gameLimits/limitsFacade";
import { LimitLevel } from "../../../skywind/models/limitLevels";
import { getConfigurationFacade } from "../../../skywind/services/gameLimits/defaultConfigurationFacade";
import { GameLimitsFixtures } from "./fixtures";
import {
    GameLimitsPermissionType,
    LIMIT_CONFIGURATION_TYPE,
    SchemaDefinition
} from "../../../skywind/entities/schemaDefinition";

const fixtures = new GameLimitsFixtures();

@suite("NewLimitsConfigurationSpec", timeout(20000))
class NewLimitsConfigurationSpec {
    private static definitionService = getSchemaDefinitionService();
    private static configurationService = getSchemaConfigurationService();
    private static limitsService: GameLimitsConfigurationService;
    public static master: BaseEntity;
    public static brand: BrandEntity;
    public static brandWithCustomLevels: BrandEntity;
    public static parent;
    public static slotDefinitionIds = [];
    public static merchant: BaseEntity;
    public static customMultipliers: CurrencyMultiplier;
    public static merchantEntityGame: EntityGame;
    public static segmentIds: number[] = [];
    public static brandWithCustomMultipliers: BrandEntity;
    public static entityGameForCustomBrand: EntityGame;
    public static entityGame: EntityGame;
    public static definitionWithSmartRounding: SchemaDefinition;
    public static multiLevelBacDefinition: SchemaDefinition;
    public static gameGroup;
    public static highLevel: LimitLevel;
    public static midLevel: LimitLevel;
    public static lowLevel: LimitLevel;
    public static highestLevel: LimitLevel;
    public static superMidLevel: LimitLevel;
    public static superLowLevel: LimitLevel;
    public static superHighLevel: LimitLevel;

    public static async before() {
        await truncate();
        NewLimitsConfigurationSpec.master = await createComplexStructure();
        const parent = await NewLimitsConfigurationSpec.master.find({ path: ":TLE1:" });
        NewLimitsConfigurationSpec.parent = parent;
        NewLimitsConfigurationSpec.limitsService = getGameLimitsConfigurationService(NewLimitsConfigurationSpec.master);
        parent.addCurrency("GBP");
        parent.addCurrency("BIF");
        parent.addCurrency("CNY");
        parent.addCurrency("AED");
        parent.addCurrency("RUP");
        parent.addCurrency("SEK");
        parent.addCurrency("USD");
        parent.save();
        NewLimitsConfigurationSpec.brand = await factory.create(FACTORY.BRAND, {}, {
            parent
        });
        NewLimitsConfigurationSpec.brandWithCustomLevels = await factory.create(FACTORY.BRAND, {}, {
            parent,
            currencies: ["GBP", "BIF", "CNY", "AED", "RUP", "SEK", "USD", "EUR"]
        });
        const merch = await factory.create(FACTORY.MERCHANT, {}, {
            parent
        });

        NewLimitsConfigurationSpec.merchant = await EntityService.findOne({ id: merch.brandId });
        NewLimitsConfigurationSpec.merchant.addCurrency("GBP");
        NewLimitsConfigurationSpec.merchant.addCurrency("BIF");
        NewLimitsConfigurationSpec.merchant.addCurrency("MAD");
        NewLimitsConfigurationSpec.merchant.addCurrency("AED");
        NewLimitsConfigurationSpec.merchant.addCurrency("RUP");
        NewLimitsConfigurationSpec.merchant.addCurrency("USD");
        NewLimitsConfigurationSpec.merchant.save();

        NewLimitsConfigurationSpec.customMultipliers = await factory.create(FACTORY.CURRENCY_MULTIPLIER, {}, {
            entityId: NewLimitsConfigurationSpec.merchant.id,
            baseCurrency: "USD",
            multipliers: [
                {
                    currencyCode: "GBP",
                    currencyMultiplier: 1
                }, {
                    currencyCode: "MAD",
                    currencyMultiplier: 100
                }, {
                    currencyMultiplier: 20,
                    currencyCode: "AED"
                }, {
                    currencyMultiplier: 15,
                    currencyCode: "RUP"
                }
            ]
        });

        for (let i = 0; i < 2; i++) {
            const definition = await factory.create(FACTORY.SCHEMA_DEFINITION);
            NewLimitsConfigurationSpec.slotDefinitionIds.push(definition.id);
            await NewLimitsConfigurationSpec.configurationService.create(fixtures.getSchemaConfiguration(definition,
                1));
        }

        const limitsConfiguration: CreateGameLimitsConfiguration = {
            "schemaDefinitionId": NewLimitsConfigurationSpec.slotDefinitionIds[0],
            "filters": {
                "EUR": {
                    "maxTotalBet": 300,
                    "stakeAll": [
                        0.01, 0.02, 0.03, 0.05, 0.08, 0.1, 0.2, 0.3, 0.5, 0.8, 1, 2, 3, 5, 8,
                        10, 20, 30, 50, 60, 80, 90, 100, 150, 200, 250, 300
                    ],
                    "defaultTotalStake": 1.5,
                    "winMax": 500000
                }
            } as any
        };
        await NewLimitsConfigurationSpec.limitsService.create(NewLimitsConfigurationSpec.master, limitsConfiguration);
        const overridePropsLimitsConfiguration: CreateGameLimitsConfiguration = {
            "schemaDefinitionId": NewLimitsConfigurationSpec.slotDefinitionIds[1],
            gameLimits: {
                "EUR": {
                    stakeAll: [1, 2, 3, 5, 10],
                    defaultTotalStake: 1.5,
                    winMax: 500000
                } as any,
                "CNY": {
                    winMax: 30000
                }
            }
        };
        await NewLimitsConfigurationSpec.limitsService.create(NewLimitsConfigurationSpec.master,
            overridePropsLimitsConfiguration);

        NewLimitsConfigurationSpec.merchantEntityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            entityId: NewLimitsConfigurationSpec.merchant.id,
            gameBuildOptions: {
                schemaDefinitionId: NewLimitsConfigurationSpec.slotDefinitionIds[1],
                totalBetMultiplier: 10
            }
        });

        NewLimitsConfigurationSpec.entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            entityId: NewLimitsConfigurationSpec.brand.id,
            gameBuildOptions: {
                schemaDefinitionId: NewLimitsConfigurationSpec.slotDefinitionIds[1],
                totalBetMultiplier: 10
            }
        });

        let createdLimits = await NewLimitsConfigurationSpec.limitsService.create(NewLimitsConfigurationSpec.merchant,
            {
                schemaDefinitionId: NewLimitsConfigurationSpec.slotDefinitionIds[1],
                gameCode: NewLimitsConfigurationSpec.merchantEntityGame.game.code,
                gameLimits: {
                    "USD": {
                        stakeAll: [1, 2, 3, 5, 10]
                    }
                },
                segment: { segment: { currency: ["USD"] } }
            },
            true
        );
        NewLimitsConfigurationSpec.segmentIds.push(createdLimits.segmentId);

        createdLimits = await NewLimitsConfigurationSpec.limitsService.create(NewLimitsConfigurationSpec.merchant,
            {
                schemaDefinitionId: NewLimitsConfigurationSpec.slotDefinitionIds[1],
                gameCode: NewLimitsConfigurationSpec.merchantEntityGame.game.code,
                gameLimits: {
                    "USD": {
                        stakeAll: [
                            0.01, 0.02, 0.03, 0.05, 0.08, 0.1, 0.2, 0.3, 0.5, 0.8, 1, 2, 3, 5, 8, 10,
                            20, 30, 50, 60, 80, 90, 100, 150, 200, 250, 300
                        ]
                    }
                },
                segment: { segment: { currency: ["RUP"] } }
            },
            true
        );
        NewLimitsConfigurationSpec.segmentIds.push(createdLimits.segmentId);

        const slotSchemaDefinitionWithCoins = await factory.attrs(FACTORY.SCHEMA_DEFINITION, {}, {
            schema: {
                type: "object",
                required: ["stakeAll", "defaultTotalStake", "winMax"],
                properties: {
                    "stakeAll": {
                        "type": "array",
                        "title": "Stakes",
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE,
                        "items": {
                            "type": "number"
                        }
                    },
                    "defaultTotalStake": {
                        "type": "number",
                        "title": "Default Total Bet",
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
                    },
                    "winMax": {
                        "type": "number",
                        "title": "Win Capping",
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
                    },
                    "stakeDef": {
                        "type": "number",
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED
                    },
                    "stakeMax": {
                        "type": "number",
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED
                    },
                    "maxTotalStake": {
                        "type": "number",
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED
                    },
                    "stakeMin": {
                        "type": "number",
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED
                    },
                    "coins": {
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONST,
                        "items": { "type": "number" },
                        "type": "array",
                        "title": "Coins"
                    },
                    "defaultCoin": {
                        type: "number",
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONST
                    }
                }
            }
        });
        const definitionWithCoins = await NewLimitsConfigurationSpec.definitionService.create(
            slotSchemaDefinitionWithCoins);
        NewLimitsConfigurationSpec.slotDefinitionIds.push(definitionWithCoins.id);
        const configurationWithCoins: SchemaConfiguration = {
            schemaDefinitionId: definitionWithCoins.id,
            entityId: NewLimitsConfigurationSpec.master.id,
            name: "Slot Games configuration",
            configuration: {
                "EUR": {
                    "stakeAll": [
                        0.01,
                        0.02,
                        0.03,
                        0.05,
                        0.08,
                        0.1
                    ],
                    "defaultTotalStake": 1.5,
                    "winMax": 500000,
                    coins: [1, 2, 3, 4],
                    defaultCoin: 3
                }
            }
        };
        await NewLimitsConfigurationSpec.configurationService.create(configurationWithCoins);

        await NewLimitsConfigurationSpec.limitsService.create(NewLimitsConfigurationSpec.master, {
            "schemaDefinitionId": definitionWithCoins.id,
            "gameLimits": {
                "EUR": {
                    "stakeAll": [
                        0.01,
                        0.02,
                        0.03,
                        0.05,
                        0.08,
                        0.1
                    ],
                    "defaultTotalStake": 1.5,
                    "winMax": 500000,
                    coins: [1, 2, 3, 4],
                    defaultCoin: 3
                }
            },
            "filters": {
                "EUR": { "maxTotalBet": 300 }
            }
        });

        const bacDefinition = await factory.create(FACTORY.SCHEMA_DEFINITION_LIVE);
        NewLimitsConfigurationSpec.slotDefinitionIds.push(bacDefinition.id);

        const [high, mid, low, highest, superMid] = await factory.createMany(FACTORY.LIMIT_LEVEL, 5, 
            [{ title: "high"}, { title: "mid"}, { title: "low"}, { title: "highest"}, { title: "superMid"}]);
        NewLimitsConfigurationSpec.highLevel = high.toInfo(true);
        NewLimitsConfigurationSpec.midLevel = mid.toInfo(true);
        NewLimitsConfigurationSpec.lowLevel = low.toInfo(true);
        NewLimitsConfigurationSpec.highestLevel = highest.toInfo(true);
        NewLimitsConfigurationSpec.superMidLevel = superMid.toInfo(true);

        const masterGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
            entityId: 1,
            gameBuildOptions: {
                code: "testGame1",
                schemaDefinitionId: bacDefinition.id,
                features: { live: {} }
            }
        });
        await NewLimitsConfigurationSpec.limitsService.create(NewLimitsConfigurationSpec.master, {
            schemaDefinitionId: bacDefinition.id,
            gameCode: "testGame1",
            levels: [NewLimitsConfigurationSpec.highLevel.pid, NewLimitsConfigurationSpec.lowLevel.pid],
            gameLimits: {
                "EUR": {
                    [NewLimitsConfigurationSpec.highLevel.pid]: {
                        "bets": {
                            "playerPair": { "max": 90000, "exposure": 60000, "min": 500, payout: 12 },
                            "egalite": { "max": 50000, "exposure": 30000, "min": 500, payout: 221 },
                            "big": { "max": 500, "exposure": 350000, "min": 500, payout: 1.54 }
                        },
                        "totalStakeMin": 5,
                        "stakeAll": [500, 1000, 2500, 5000, 10000],
                        "stakeDef": 10,
                        "totalStakeMax": 10000,
                        "defaultTotalStake": 500,
                        concurrentPlayers: 100
                    },
                    [NewLimitsConfigurationSpec.lowLevel.pid]: {
                        "bets": {
                            "playerPair": { "max": 500, "exposure": 300000, "min": 1, payout: 12 },
                            "egalite": { "max": 25, "exposure": 15000, "min": 1, payout: 221 },
                            "big": { "max": 250, "exposure": 150000, "min": 1, payout: 1.54 }
                        },
                        "totalStakeMin": 1,
                        "stakeDef": 1,
                        "defaultTotalStake": 1,
                        "totalStakeMax": 2500,
                        "stakeAll": [1, 3, 5, 10, 25, 50, 100],
                        "concurrentPlayers": 100,
                        isDefaultRoom: true
                    }
                },
                "CNY": {
                    [NewLimitsConfigurationSpec.lowLevel.pid]: {
                        "bets": {
                            "playerPair": { "max": 900, "exposure": 60000, "min": 5 },
                            "egalite": { "max": 50000, "exposure": 30000, "min": 5 },
                            "big": { "max": 600, "exposure": 350000, "min": 5 }
                        },
                        "totalStakeMin": 5,
                        "stakeAll": [100, 250, 500, 1000]
                    }
                },
                "BNS": {
                    [NewLimitsConfigurationSpec.highLevel.pid]: {
                        "bets": {
                            "playerPair": { "max": 1000, "exposure": 600000, "min": 5 },
                            "egalite": { "max": 50, "exposure": 30000, "min": 5 },
                            "big": { "max": 500, "exposure": 300000, "min": 5 }
                        },
                        "totalStakeMin": 5,
                        "stakeDef": 10,
                        "totalStakeMax": 10000,
                        "stakeAll": [10, 30, 50, 100]
                    },
                    [NewLimitsConfigurationSpec.lowLevel.pid]: {
                        "bets": {
                            "playerPair": { "max": 500, "exposure": 300000, "min": 1, payout: 11 },
                            "egalite": { "max": 25, "exposure": 15000, "min": 1 },
                            "big": { "max": 250, "exposure": 150000, "min": 1 }
                        },
                        "stakeDef": 1,
                        "totalStakeMax": 2500,
                        "totalStakeMin": 50,
                        "stakeAll": [1, 3, 5, 10]
                    }
                },
                "VEF": {
                    aligned: true,
                    [NewLimitsConfigurationSpec.lowLevel.pid]: {
                        stakeAll: [0.01, 0.05]
                    }
                }
            }
        } as any);

        NewLimitsConfigurationSpec.multiLevelBacDefinition = await factory.create(FACTORY.SCHEMA_DEFINITION_LIVE,
            {},
            { name: "multi" });

        const superLow = await factory.create(FACTORY.LIMIT_LEVEL, {}, { title: "superLow"});
        NewLimitsConfigurationSpec.superLowLevel = superLow.toInfo(true);

        const masterGame2 = await factory.create(FACTORY.ENTITY_GAME, {}, {
            entityId: 1,
            gameBuildOptions: {
                code: "testGame2",
                schemaDefinitionId: NewLimitsConfigurationSpec.multiLevelBacDefinition.id,
                features: { live: {} }
            }
        });

        await NewLimitsConfigurationSpec.limitsService.create(NewLimitsConfigurationSpec.master, {
            schemaDefinitionId: NewLimitsConfigurationSpec.multiLevelBacDefinition.id,
            gameCode: "testGame2",
            levels: [NewLimitsConfigurationSpec.highLevel.pid, NewLimitsConfigurationSpec.lowLevel.pid],
            gameLimits: {
                "EUR": {
                    [NewLimitsConfigurationSpec.highLevel.pid]: {
                        "bets": {
                            "playerPair": { "max": 90000, "exposure": 60000, "min": 500, payout: 12 },
                            "egalite": { "max": 50000, "exposure": 30000, "min": 500, payout: 221 },
                            "big": { "max": 500, "exposure": 350000, "min": 500, payout: 1.54  }
                        },
                        "totalStakeMin": 5,
                        "stakeAll": [500, 1000, 2500, 5000, 10000],
                        "totalStakeMax": 10000,
                        "defaultTotalStake": 500,
                        concurrentPlayers: 100
                    },
                    [NewLimitsConfigurationSpec.lowLevel.pid]: {
                        "bets": {
                            "playerPair": { "max": 500, "exposure": 300000, "min": 1, payout: 12 },
                            "egalite": { "max": 25, "exposure": 15000, "min": 1, payout: 221 },
                            "big": { "max": 250, "exposure": 150000, "min": 1, payout: 1.54 }
                        },
                        "totalStakeMin": 1,
                        "stakeDef": 1,
                        "defaultTotalStake": 1,
                        "totalStakeMax": 2500,
                        "stakeAll": [1, 3, 5, 10, 25, 50, 100],
                        "concurrentPlayers": 100,
                        isDefaultRoom: true
                    }
                }
            }
        } as any);
        
        const parentGame2 = await factory.create(FACTORY.ENTITY_GAME, {}, {
            entityId: NewLimitsConfigurationSpec.parent.id,
            parentEntityGameId: masterGame2.id,
            gameId: masterGame2.gameId 
        });

        await NewLimitsConfigurationSpec.limitsService.create(NewLimitsConfigurationSpec.parent, {
            schemaDefinitionId: NewLimitsConfigurationSpec.multiLevelBacDefinition.id,
            gameCode: "testGame2",
            gameLimits: {
                EUR: {
                    [NewLimitsConfigurationSpec.superLowLevel.pid]: {
                        "bets": {
                            "playerPair": { "max": 125, "exposure": 300000, "min": 1, payout: 12 },
                            "egalite": { "max": 25, "exposure": 15000, "min": 1, payout: 221 },
                            "big": { "max": 250, "exposure": 150000, "min": 1, payout: 1.54 }
                        },
                        "totalStakeMin": 1,
                        "stakeDef": 1,
                        "defaultTotalStake": 1,
                        "totalStakeMax": 2500,
                        "stakeAll": [1, 3, 5, 10, 15, 25, 50, 75, 100],
                        "concurrentPlayers": 100
                    }
                }
            },
            levels: [
                NewLimitsConfigurationSpec.lowLevel.pid,
                NewLimitsConfigurationSpec.highLevel.pid,
                NewLimitsConfigurationSpec.superLowLevel.pid
            ],
            defaultLevel: NewLimitsConfigurationSpec.superLowLevel.pid
        });

        const superHigh = await factory.create(FACTORY.LIMIT_LEVEL, {}, { title: "superHigh"});
        NewLimitsConfigurationSpec.superHighLevel = superHigh.toInfo(true);

        await factory.create(FACTORY.ENTITY_GAME, {}, {
            entityId: NewLimitsConfigurationSpec.brandWithCustomLevels.id,
            gameId: parentGame2.gameId,
            parentEntityGameId: parentGame2.id
        });

        await NewLimitsConfigurationSpec.limitsService.create(NewLimitsConfigurationSpec.brandWithCustomLevels, {
            schemaDefinitionId: NewLimitsConfigurationSpec.multiLevelBacDefinition.id,
            gameCode: "testGame2",
            gameLimits: {
                EUR: {
                    [NewLimitsConfigurationSpec.superHighLevel.pid]: {
                        "bets": {
                            "playerPair": { "max": 125, "exposure": 300000, "min": 1, payout: 12 },
                            "egalite": { "max": 25, "exposure": 15000, "min": 1, payout: 221 },
                            "big": { "max": 250, "exposure": 150000, "min": 1, payout: 1.54 }
                        },
                        "totalStakeMin": 1,
                        "stakeDef": 1,
                        "defaultTotalStake": 1,
                        "totalStakeMax": 2500,
                        "stakeAll": [1, 3, 5, 10, 15, 25, 50, 75, 100, 500],
                        "concurrentPlayers": 100
                    }
                }
            },
            levels: [
                NewLimitsConfigurationSpec.highLevel.pid,
                NewLimitsConfigurationSpec.lowLevel.pid,
                NewLimitsConfigurationSpec.superLowLevel.pid,
                NewLimitsConfigurationSpec.superHighLevel.pid
            ],
            defaultLevel: NewLimitsConfigurationSpec.superLowLevel.pid
        });

        const parentGame1 = await factory.create(FACTORY.ENTITY_GAME, {}, {
            entityId: NewLimitsConfigurationSpec.parent.id,
            gameId: masterGame.gameId,
            parentEntityGameId: masterGame.id
        });
        await factory.create(FACTORY.ENTITY_GAME, {}, {
            entityId: NewLimitsConfigurationSpec.brandWithCustomLevels.id,
            gameId: parentGame1.gameId,
            parentEntityGameId: parentGame1.id
        });

        await NewLimitsConfigurationSpec.limitsService.create(NewLimitsConfigurationSpec.brandWithCustomLevels, {
            schemaDefinitionId: bacDefinition.id,
            gameCode: "testGame1",
            levels: [NewLimitsConfigurationSpec.highLevel.pid]
        });

        const fixedStakeAllDefinition: SchemaDefinition = {
            name: "Slot games with fixed stakeAll ",
            schema: {
                "type": "object",
                "properties": {
                    "stakeDef": { "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED, "type": "number" },
                    "stakeMax": { "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED, "type": "number" },
                    "maxTotalStake": {
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED,
                        "type": "number"
                    },
                    "stakeMin": { "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED, "type": "number" },
                    "stakeAll": {
                        "items": { "type": "number" },
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.FIXED,
                        "type": "array",
                        "title": "Stakes"
                    },
                    "winMax": {
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE,
                        "type": "number",
                        "title": "Win Capping"
                    },
                    "defaultTotalStake": {
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE,
                        "type": "number",
                        "title": "Default total stake"
                    },
                    "customParam": { "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.FIXED, "type": "number" }
                },
                levels: false
            }
        };
        const fixedStakeAllDef = await NewLimitsConfigurationSpec.definitionService.create(fixedStakeAllDefinition);
        NewLimitsConfigurationSpec.slotDefinitionIds.push(fixedStakeAllDef.id);
        const fixedStakeAllConfiguration: SchemaConfiguration = {
            schemaDefinitionId: fixedStakeAllDef.id,
            entityId: NewLimitsConfigurationSpec.master.id,
            name: "Slot Games configuration",
            configuration: {
                "EUR": {
                    stakeAll: [0.04, 0.1, 0.2, 0.4],
                    defaultTotalStake: 1.5,
                    winMax: 500000,
                    customParam: 2
                }
            }
        };
        await NewLimitsConfigurationSpec.configurationService.create(fixedStakeAllConfiguration);

        const constForAllCurrenciesDefinition: SchemaDefinition = {
            name: "Slot games with constForAllCurrencies for stakeAll and winMax",
            schema: {
                levels: false,
                "type": "object",
                "properties": {
                    "winMax": {
                        "type": "number",
                        "title": "Win Capping",
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONST
                    },
                    "stakeAll": {
                        "type": "array",
                        "items": {
                            "type": "number"
                        },
                        "title": "Stakes",
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONST
                    },
                    "stakeDef": {
                        "type": "number",
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED
                    },
                    "stakeMax": {
                        "type": "number",
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED
                    },
                    "stakeMin": {
                        "type": "number",
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED
                    },
                    "maxTotalStake": {
                        "type": "number",
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED
                    }
                }
            },
            definitions: null,
            permissions: {
                default: {
                    winMax: GameLimitsPermissionType.ADMIN,
                    stakeAll: GameLimitsPermissionType.ENTITY
                }
            }
        };

        const constForAllCurrenciesDef = await NewLimitsConfigurationSpec.definitionService
            .create(constForAllCurrenciesDefinition);

        NewLimitsConfigurationSpec.slotDefinitionIds.push(constForAllCurrenciesDef.id);
        const constForAllCurrenciesConfiguration: SchemaConfiguration = {
            schemaDefinitionId: constForAllCurrenciesDef.id,
            entityId: NewLimitsConfigurationSpec.master.id,
            name: "Slot Games configuration with constForAllCurrencies",
            configuration: {
                "EUR": {
                    stakeAll: [0.03, 0.6, 0.7, 0.8],
                    defaultTotalStake: 1.5,
                    winMax: 2500
                }
            }
        };
        await NewLimitsConfigurationSpec.configurationService.create(constForAllCurrenciesConfiguration);

        const liveBaccaratConstForAllCurrenciesStakeAllDefinition: any = {
            name: "Live Baccarat with StakeAll = 'constForAllCurrencies'",
            definitions: {
                "bet": {
                    "type": "object",
                    "properties": {
                        "max": {
                            "type": "number",
                            "title": "Maximum",
                            "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
                        },
                        "min": {
                            "type": "number",
                            "title": "Minimum",
                            "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
                        },
                        "exposure": {
                            "type": "number",
                            "title": " Limit  for all players in single round",
                            "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED
                        },
                        "payout": {
                            "type": "number",
                            "title": "Payout of bet type",
                            "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONST

                        }
                    }
                }
            },
            schema: {
                type: "object",
                properties: {
                    "bets": {
                        "type": "object",
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE,
                        "properties": {
                            "playerPair": {
                                "$ref": "/bet"
                            },
                            "egalite": {
                                "$ref": "/bet"
                            },
                            "big": {
                                "$ref": "/bet"
                            }
                        }
                    },
                    "stakeAll": {
                        "title": "Stakes",
                        "type": "array",
                        "items": {
                            "type": "number"
                        },
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONST
                    },
                    "totalStakeMin": {
                        "type": "number",
                        "title": "Minimum total bet",
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
                    },
                    "totalStakeMax": {
                        "type": "number",
                        "title": "Maximum total bet",
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
                    },
                    "stakeDef": {
                        "type": "number",
                        "title": "Default stake",
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE
                    },
                    "stakeMax": {
                        "type": "number",
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED
                    },
                    "stakeMin": {
                        "type": "number",
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED
                    },
                    "concurrentPlayers": {
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONST,
                        "type": "number",
                        "title": "Concurrent Players"
                    },
                    "defaultTotalStake": {
                        "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE,
                        "type": "number",
                        "title": "Default total stake"
                    }
                },
                levels: true
            },
            permissions: {
                "EUR": {
                    "bets": "entity"
                },
                "CNY": {
                    "bets": "entity"
                },
                "BNS": {
                    "bets": "entity"
                }
            }
        };
        const bacConstForAllCurrenciesStakeAllDefinition = await NewLimitsConfigurationSpec.definitionService
            .create(liveBaccaratConstForAllCurrenciesStakeAllDefinition);
        NewLimitsConfigurationSpec.slotDefinitionIds.push(bacConstForAllCurrenciesStakeAllDefinition.id);

        const masterGame3 = await factory.create(FACTORY.ENTITY_GAME, {}, {
            entityId: 1,
            gameBuildOptions: {
                code: "testGame3",
                schemaDefinitionId: bacConstForAllCurrenciesStakeAllDefinition.id,
                features: { live: {} }
            }
        });
        
        const parentGame3 = await factory.create(FACTORY.ENTITY_GAME, {}, {
            entityId: NewLimitsConfigurationSpec.parent.id,
            gameId: masterGame3.gameId,
            parentEntityGameId: masterGame3.id
        });

        await factory.create(FACTORY.ENTITY_GAME, {}, {
            entityId: NewLimitsConfigurationSpec.brand.id,
            gameId: parentGame3.gameId,
            parentEntityGameId: parentGame3.id
        });

        await NewLimitsConfigurationSpec.limitsService.create(NewLimitsConfigurationSpec.master, {
            schemaDefinitionId: bacConstForAllCurrenciesStakeAllDefinition.id,
            gameCode: "testGame3",
            levels: [NewLimitsConfigurationSpec.highLevel.pid, NewLimitsConfigurationSpec.lowLevel.pid],
            gameLimits: {
                "EUR": {
                    [NewLimitsConfigurationSpec.highLevel.pid]: {
                        "bets": {
                            "playerPair": { "max": 1000, "exposure": 600000, "min": 5, payout: 12 },
                            "egalite": { "max": 50, "exposure": 30000, "min": 5, payout: 221 },
                            "big": { "max": 500, "exposure": 300000, "min": 5, payout: 1.54 }
                        },
                        "totalStakeMin": 5,
                        "stakeDef": 10,
                        defaultTotalStake: 1,
                        "totalStakeMax": 10000,
                        "stakeAll": [1, 3, 5, 10, 25],
                        concurrentPlayers: 100
                    },
                    [NewLimitsConfigurationSpec.lowLevel.pid]: {
                        "bets": {
                            "playerPair": { "max": 500, "exposure": 300000, "min": 1, payout: 12 },
                            "egalite": { "max": 25, "exposure": 15000, "min": 1, payout: 221 },
                            "big": { "max": 250, "exposure": 150000, "min": 1, payout: 1.54 }
                        },
                        "totalStakeMin": 1,
                        "stakeDef": 1,
                        defaultTotalStake: 1,
                        "totalStakeMax": 2500,
                        "stakeAll": [1, 3, 5, 10],
                        concurrentPlayers: 100
                    }
                }
            }
        });
        
        NewLimitsConfigurationSpec.brandWithCustomMultipliers = await factory.create(FACTORY.BRAND, {}, {
            parent
        });

        await new EntitySettingsService(NewLimitsConfigurationSpec.brandWithCustomMultipliers)
            .patch({ allowToOverrideDefaultLimits: true });
        NewLimitsConfigurationSpec.brandWithCustomMultipliers.addCurrency("SEK");
        NewLimitsConfigurationSpec.brandWithCustomMultipliers.addCurrency("USD");
        NewLimitsConfigurationSpec.brandWithCustomMultipliers.save();

        NewLimitsConfigurationSpec.entityGameForCustomBrand = await factory.create(FACTORY.ENTITY_GAME, {}, {
            gameBuildOptions: {
                schemaDefinitionId: definitionWithCoins.id,
                code: "sw_custom_multiplier",
                totalBetMultiplier: 1
            },
            entityId: NewLimitsConfigurationSpec.brandWithCustomMultipliers.id
        });

        await getCurrencyMultiplierService().createOrUpdate(NewLimitsConfigurationSpec.brandWithCustomMultipliers, {
            "baseCurrency": "SEK",
            "multipliers": [
                {
                    "currencyCode": "USD",
                    "currencyMultiplier": 10
                }
            ]
        });

        await NewLimitsConfigurationSpec.limitsService.create(NewLimitsConfigurationSpec.brandWithCustomMultipliers, {
            "schemaDefinitionId": definitionWithCoins.id,
            "gameLimits": {
                "SEK": { "stakeAll": [0.2, 0.4, 0.5, 0.7, 1, 2, 3, 5, 8, 9, 11] }
            },
            "gameCode": "sw_custom_multiplier",
            "segment": {
                externalId: 1,
                segment: {}
            }
        } as any, true);

        NewLimitsConfigurationSpec.definitionWithSmartRounding = await NewLimitsConfigurationSpec.definitionService
            .create({
                name: "Smart rounding slot",
                schema: {
                    "type": "object",
                    "properties": {
                        "stakeDef": { "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CALCULATED, "type": "number" },
                        "maxTotalStake": {
                            "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE,
                            "applySmartRounding": true,
                            "type": "number"
                        },
                        "stakeAll": {
                            "items": { "type": "number" },
                            "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE,
                            "type": "array",
                            "title": "Stakes"
                        },
                        "winMax": {
                            "limitConfigurationType": LIMIT_CONFIGURATION_TYPE.CONFIGURABLE,
                            "type": "number",
                            "title": "Win Capping"
                        }
                    },
                    levels: false
                }
            });

        await NewLimitsConfigurationSpec.configurationService.create({
            schemaDefinitionId: NewLimitsConfigurationSpec.definitionWithSmartRounding.id,
            entityId: NewLimitsConfigurationSpec.master.id,
            name: "Slot Games configuration",
            configuration: {
                "EUR": {
                    "stakeAll": [0.01, 0.02, 0.03, 0.05],
                    "defaultTotalStake": 10,
                    "maxTotalStake": 1250,
                    "winMax": 500000
                }
            }
        });

        NewLimitsConfigurationSpec.gameGroup = factory.create(FACTORY.GAME_GROUP, {}, {
            brandId: NewLimitsConfigurationSpec.brandWithCustomMultipliers
        });
    }

    @test()
    public async getSlotLimitsForCustomMultipliersWithSEKBaseCurrency() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brandWithCustomMultipliers);
        let limits = await service.buildGameLaunch(
            NewLimitsConfigurationSpec.entityGameForCustomBrand.game,
            "USD", null, 3, true);
        expect(limits).deep.equal({
            coins: [
                1,
                2,
                3,
                4
            ],
            defaultCoin: 3,
            stakeAll: [2, 4, 5, 7, 10, 20, 30, 50, 80, 90, 100],
            maxTotalStake: 100,
            defaultTotalStake: 150,
            stakeDef: 100,
            stakeMax: 100,
            stakeMin: 2,
            winMax: 573236.72
        });

        limits = await service.buildGameLaunch(
            NewLimitsConfigurationSpec.entityGameForCustomBrand.game,
            "SEK", null, 3, true);
        expect(limits).deep.equal({
            coins: [
                1,
                2,
                3,
                4
            ],
            defaultCoin: 3,
            stakeAll: [0.2, 0.4, 0.5, 0.7, 1, 2, 3, 5, 8, 9, 11],
            maxTotalStake: 11,
            defaultTotalStake: 15,
            stakeDef: 11,
            stakeMax: 11,
            stakeMin: 0.2,
            winMax: 5159136.24
        });
    }

    @test()
    public async getSlotLimitsForDefaultCurrency() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brand);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.slotDefinitionIds[0], 50), "EUR");
        expect(limits).deep.equal({
            defaultTotalStake: 1.5,
            stakeAll: [0.01, 0.02, 0.03, 0.05, 0.08, 0.1, 0.2, 0.3, 0.5, 0.8, 1, 2, 3, 5],
            maxTotalStake: 250,
            stakeDef: 0.03,
            stakeMax: 5,
            stakeMin: 0.01,
            winMax: 500000
        });
    }

    @test()
    public async getSlotLimitsForUsd() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brand);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.slotDefinitionIds[0], 50), "USD");
        expect(limits).deep.equal({
            defaultTotalStake: 1.5,
            stakeAll: [0.01, 0.02, 0.03, 0.05, 0.08, 0.1, 0.2, 0.3, 0.5, 0.8, 1, 2, 3, 5],
            maxTotalStake: 250,
            stakeDef: 0.03,
            stakeMax: 5,
            stakeMin: 0.01,
            winMax: 573236.72
        });

    }

    @test()
    public async getSlotLimitsForAligned() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brand);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.slotDefinitionIds[0], 50), "MOP");
        expect(limits).deep.equal({
            defaultTotalStake: 15,
            stakeAll: [0.01, 0.02, 0.03, 0.05, 0.1, 0.2, 0.3, 0.5, 0.8, 1, 2, 3, 5, 8, 10, 20, 30, 50],
            maxTotalStake: 2500,
            stakeDef: 0.3,
            stakeMax: 50,
            stakeMin: 0.01,
            winMax: 4627805.99
        });
    }

    @test()
    public async getSlotLimitsForMyr() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brand);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.slotDefinitionIds[0], 50), "MYR");

        expect(limits).deep.equal({
            defaultTotalStake: 7.5,
            stakeAll: [0.05, 0.1, 0.2, 0.3, 0.4, 0.5, 1, 2, 3, 4, 5, 10, 20, 30],
            maxTotalStake: 1500,
            stakeDef: 0.1,
            stakeMax: 30,
            stakeMin: 0.05,
            winMax: 2384378.15
        });
    }

    @test()
    public async getSlotLimitsForNotAligned() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brand);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.slotDefinitionIds[0], 50), "BNS");
        expect(limits).deep.equal({
            defaultTotalStake: 1500,
            maxTotalStake: 15000000,
            stakeAll: [
                10,
                20,
                30,
                50,
                80,
                100,
                200,
                300,
                500,
                800,
                1000,
                2000,
                3000,
                5000,
                8000,
                10000,
                20000,
                30000,
                50000,
                60000,
                80000,
                90000,
                100000,
                150000,
                200000,
                250000,
                300000
            ],
            stakeDef: 30,
            stakeMax: 300000,
            stakeMin: 10,
            winMax: 500000000
        });
    }

    @test()
    public async getSlotLimitsFromLimitsConfigForDefaultCurrency() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brand);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.slotDefinitionIds[1], 10), "EUR");
        expect(limits).deep.equal({
            defaultTotalStake: 1.5,
            stakeAll: [1, 2, 3, 5, 10],
            maxTotalStake: 100,
            stakeDef: 1,
            stakeMax: 10,
            stakeMin: 1,
            winMax: 500000
        });
        const currencyLimits = await service.buildForAllCurrencies(NewLimitsConfigurationSpec.entityGame.game.code);
        expect(currencyLimits["USD"]).deep.equal({
            ...limits,
            winMax: 573236.72
        });
        expect(Object.keys(currencyLimits)).deep.eq(["USD"]);
    }

    @test()
    public async getSlotLimitsFromLimitsConfigForCNY() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brand);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.slotDefinitionIds[1], 10), "CNY");
        expect(limits).deep.equal({
            defaultTotalStake: 15,
            stakeAll: [0.01, 0.02, 0.03, 0.05, 10, 20, 30, 50, 100],
            maxTotalStake: 1000,
            stakeDef: 0.05,
            stakeMax: 100,
            stakeMin: 0.01,
            winMax: 30000
        });
    }

    @test()
    public async getSlotLimitsFromLimitsConfigForEURWithCustomMultipliers() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.merchant);
        const limits = await service.buildGameLaunch(
            NewLimitsConfigurationSpec.merchantEntityGame.game,
            "USD", NewLimitsConfigurationSpec.segmentIds[0]);
        // base currency in custom multipliers (not EUR)
        expect(limits).deep.equal({
            defaultTotalStake: 1.5,
            stakeAll: [1, 2, 3, 5, 10],
            maxTotalStake: 100,
            stakeDef: 1,
            stakeMax: 10,
            stakeMin: 1,
            winMax: 573236.72
        });
    }

    @test()
    public async getSlotLimitsFromLimitsConfigForMADWithCustomMultipliers() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.merchant);
        const limits = await service.buildGameLaunch(
            NewLimitsConfigurationSpec.merchantEntityGame.game,
            "MAD", null, NewLimitsConfigurationSpec.segmentIds[0], true);
        // converted limits without aligned values
        expect(limits).deep.equal({
            defaultTotalStake: 150,
            stakeAll: [100, 200, 300, 500, 1000],
            maxTotalStake: 10000,
            stakeDef: 100,
            stakeMax: 1000,
            stakeMin: 100,
            winMax: 5442653.36
        });
    }

    @test()
    public async getSlotLimitsFromLimitsConfigForAEDWithCustomMultipliers() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.merchant);
        const limits = await service.buildGameLaunch(
            NewLimitsConfigurationSpec.merchantEntityGame.game,
            "AED",
            null,
            NewLimitsConfigurationSpec.segmentIds[0],
            true);

        // only converted limits
        expect(limits).deep.equal({
            defaultTotalStake: 30,
            stakeAll: [20, 40, 60, 100, 200],
            maxTotalStake: 2000,
            stakeDef: 20,
            stakeMax: 200,
            stakeMin: 20,
            winMax: 2105602.22
        });
    }

    @test()
    public async getSlotLimitsFromLimitsConfigForRUPWithCustomMultipliers() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.merchant);

        const limits = await service.buildGameLaunch(
            NewLimitsConfigurationSpec.merchantEntityGame.game,
            "RUP",
            null,
            NewLimitsConfigurationSpec.segmentIds[1]);

        // only converted limits
        expect(limits).deep.equal({
            defaultTotalStake: 15,
            stakeAll: [
                0.1, 0.2, 0.3, 0.5, 0.8, 1, 2, 3, 5, 8, 10, 20, 30, 50, 80, 100,
                200, 300, 500, 600, 800, 900, 1000, 1500, 2000, 2500, 3000
            ],
            maxTotalStake: 30000,
            stakeDef: 2,
            stakeMax: 3000,
            stakeMin: 0.1,
            winMax: 42249.782
        });
    }

    @test()
    public async getConstForAllCurrenciesConfigForRUPWithCustomMultipliers() {
        const definition = await NewLimitsConfigurationSpec.definitionService
            .retrieve(NewLimitsConfigurationSpec.slotDefinitionIds[5]);
        const facade = await getConfigurationFacade(definition);
        const currencyMultipliers = await getCurrencyMultiplierService().findOne(NewLimitsConfigurationSpec.merchant);

        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.merchant);

        const limits = await service.build(
            facade,
            30,
            "RUP",
            { gameLimits: { EUR: { defaultTotalStake: 0.03 } } } as any,
            true,
            currencyMultipliers);

        // only converted limits
        expect(limits).deep.equal({
            "maxTotalStake": 24,
            "stakeAll": [
                0.03,
                0.6,
                0.7,
                0.8
            ],
            "stakeDef": 0.03,
            "stakeMax": 0.8,
            "stakeMin": 0.03,
            "winMax": 211.248
        });
    }

    @test()
    public async getSlotLimitsWithCoinsEur() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brand);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.slotDefinitionIds[2], 10), "EUR");
        expect(limits).deep.equal({
            defaultTotalStake: 1.5,
            stakeAll: [0.01, 0.02, 0.03, 0.05, 0.08, 0.1],
            coins: [1, 2, 3, 4],
            defaultCoin: 3,
            maxTotalStake: 1,
            stakeDef: 0.1,
            stakeMax: 0.1,
            stakeMin: 0.01,
            winMax: 500000
        });
    }

    @test()
    public async getSlotLimitsWithCoinsCNY() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brand);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.slotDefinitionIds[2], 10), "CNY");
        expect(limits).deep.equal({
            defaultTotalStake: 15,
            stakeAll: [0.1, 0.2, 0.3, 0.5, 0.8, 1],
            maxTotalStake: 10,
            coins: [1, 2, 3, 4],
            defaultCoin: 3,
            stakeDef: 1,
            stakeMax: 1,
            stakeMin: 0.1,
            winMax: 3982562.13
        });
    }

    @test()
    public async getBacLimitsDefaultCurrency() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brand);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.slotDefinitionIds[3], undefined, "testGame1"), "EUR");
        expect(limits).deep.equal({
            [NewLimitsConfigurationSpec.highLevel.title]: {
                "bets": {
                    "big": { "exposure": 75000, "max": 500, "min": 500, "payout": 1.54 },
                    "egalite": { "exposure": 1105000000, "max": 50000, "min": 500, "payout": 221 },
                    "playerPair": { "exposure": 108000000, "max": 90000, "min": 500, "payout": 12 }
                },
                "concurrentPlayers": 100,
                "totalStakeMin": 5,
                "stakeAll": [500, 1000, 2500, 5000, 10000],
                defaultTotalStake: 500,
                "stakeDef": 10,
                "stakeMax": 10000,
                "stakeMin": 500,
                "totalStakeMax": 10000
            },
            [NewLimitsConfigurationSpec.lowLevel.title]: {
                "bets": {
                    "big": { "exposure": 40000, "max": 250, "min": 1, "payout": 1.54 },
                    "egalite": { "exposure": 555000, "max": 25, "min": 1, "payout": 221 },
                    "playerPair": { "exposure": 600000, "max": 500, "min": 1, "payout": 12 }
                },
                "concurrentPlayers": 100,
                "totalStakeMin": 1,
                "stakeDef": 1,
                stakeMax: 100,
                stakeMin: 1,
                defaultTotalStake: 1,
                "totalStakeMax": 2500,
                "stakeAll": [1, 3, 5, 10, 25, 50, 100],
                "isDefaultRoom": true
            }
        });
    }

    @test()
    public async getBacLimitsWithCustomLevels() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brandWithCustomLevels);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.slotDefinitionIds[3], undefined, "testGame1"),
            "EUR");
        expect(limits).deep.equal({
            [NewLimitsConfigurationSpec.highLevel.title]: {
                "bets": {
                    "big": { "exposure": 75000, "max": 500, "min": 500, "payout": 1.54 },
                    "egalite": { "exposure": 1105000000, "max": 50000, "min": 500, "payout": 221 },
                    "playerPair": { "exposure": 108000000, "max": 90000, "min": 500, "payout": 12 }
                },
                "concurrentPlayers": 100,
                "totalStakeMin": 5,
                "stakeAll": [500, 1000, 2500, 5000, 10000],
                defaultTotalStake: 500,
                "stakeDef": 10,
                "stakeMax": 10000,
                "stakeMin": 500,
                "totalStakeMax": 10000
            }
        });
    }

    @test()
    public async getMultiLevelsBacLimitsWithCustomLevels() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brandWithCustomLevels);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.multiLevelBacDefinition.id, undefined, "testGame2"),
            "EUR", NewLimitsConfigurationSpec.gameGroup.id) as any;
        const levels = Object.keys(limits);
        expect(levels)
            .to.have.members([
            NewLimitsConfigurationSpec.highLevel.title,
            NewLimitsConfigurationSpec.lowLevel.title,
            NewLimitsConfigurationSpec.superLowLevel.title,
            NewLimitsConfigurationSpec.superHighLevel.title
        ]);
        expect(limits[NewLimitsConfigurationSpec.lowLevel.title]).deep.equal({
            "bets": {
                "big": { "exposure": 40000, "max": 250, "min": 1, "payout": 1.54 },
                "egalite": { "exposure": 555000, "max": 25, "min": 1, "payout": 221 },
                "playerPair": { "exposure": 600000, "max": 500, "min": 1, "payout": 12 }
            },
            "concurrentPlayers": 100,
            "defaultTotalStake": 1,
            "stakeAll": [1, 3, 5, 10, 25, 50, 100],
            "stakeDef": 1,
            "stakeMax": 100,
            "stakeMin": 1,
            "totalStakeMax": 2500,
            "isDefaultRoom": true,
            "totalStakeMin": 1
        });

        const limitsUSD = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.multiLevelBacDefinition.id, undefined, "testGame2"),
            "USD", NewLimitsConfigurationSpec.gameGroup.id) as any;
        const levelsUSD = Object.keys(limits);
        expect(levelsUSD.sort()).deep.eq(levels.sort());
        expect(limitsUSD[NewLimitsConfigurationSpec.superLowLevel.title].isDefaultRoom).eq(true);

        expect(limitsUSD).deep.equal(limits);
    }

    @test()
    public async getBacLimitsCNY() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brand);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.slotDefinitionIds[3], undefined, "testGame1"),
            "CNY");
        expect(limits).deep.equal({
            [NewLimitsConfigurationSpec.highLevel.title]: {
                "bets": {
                    "big": { "exposure": 770000, "max": 5000, "min": 5000, "payout": 1.54 },
                    "egalite": { "exposure": 11050000000, "max": 500000, "min": 5000, "payout": 221 },
                    "playerPair": { "exposure": 1080000000, "max": 900000, "min": 5000, "payout": 12 }
                },
                "concurrentPlayers": 100,
                "totalStakeMin": 50,
                "stakeAll": [5000, 10000, 25000, 50000, 100000],
                defaultTotalStake: 5000,
                "stakeDef": 100,
                "stakeMax": 100000,
                "stakeMin": 5000,
                "totalStakeMax": 100000
            },
            [NewLimitsConfigurationSpec.lowLevel.title]: {
                "bets": {
                    "big": { "exposure": 90000, "max": 600, "min": 5, "payout": 1.54 },
                    "egalite": { "exposure": 1105000000, "max": 50000, "min": 5, "payout": 221 },
                    "playerPair": { "exposure": 1080000, "max": 900, "min": 5, "payout": 12 }
                },
                "concurrentPlayers": 100,
                totalStakeMin: 5,
                "stakeAll": [100, 250, 500, 1000],
                stakeMin: 100,
                stakeMax: 1000,
                stakeDef: 10,
                defaultTotalStake: 10,
                totalStakeMax: 25000,
                "isDefaultRoom": true
            }
        });
    }

    @test()
    public async getBacLimitsVEF() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brand);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.slotDefinitionIds[3], undefined, "testGame1"),
            "VEF");
        expect(limits).deep.equal({
            [NewLimitsConfigurationSpec.highLevel.title]: {
                "bets": {
                    "big": { "exposure": 770000, "max": 5000, "min": 5000, "payout": 1.54 },
                    "egalite": { "exposure": 11050000000, "max": 500000, "min": 5000, "payout": 221 },
                    "playerPair": { "exposure": 1080000000, "max": 900000, "min": 5000, "payout": 12 }
                },
                "concurrentPlayers": 100,
                "totalStakeMin": 50,
                "stakeAll": [5000, 10000, 25000, 50000, 100000],
                "stakeDef": 100,
                "stakeMax": 100000,
                "stakeMin": 5000,
                defaultTotalStake: 5000,
                "totalStakeMax": 100000
            },
            [NewLimitsConfigurationSpec.lowLevel.title]: {
                "bets": {
                    "big": { "exposure": 385000, "max": 2500, "min": 10, "payout": 1.54 },
                    "egalite": { "exposure": 5525000, "max": 250, "min": 10, "payout": 221 },
                    "playerPair": { "exposure": 6000000, "max": 5000, "min": 10, "payout": 12 }
                },
                "concurrentPlayers": 100,
                "totalStakeMin": 10,
                "stakeDef": 10,
                "totalStakeMax": 25000,
                defaultTotalStake: 10,
                "stakeAll": [0.01, 0.05, 10, 30, 50, 100, 250, 500, 1000],
                stakeMax: 1000,
                stakeMin: 0.01,
                "isDefaultRoom": true
            }
        });
    }

    @test()
    public async getBacLimitsBNS() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brand);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.slotDefinitionIds[3], undefined, "testGame1"),
            "BNS");
        expect(limits).deep.equal({
            [NewLimitsConfigurationSpec.highLevel.title]: {
                "bets": {
                    "big": { "exposure": 75000, "max": 500, "min": 5, "payout": 1.54 },
                    "egalite": { "exposure": 1105000, "max": 50, "min": 5, "payout": 221 },
                    "playerPair": { "exposure": 1200000, "max": 1000, "min": 5, "payout": 12 }
                },
                "concurrentPlayers": 100,
                "totalStakeMin": 5,
                "stakeDef": 10,
                "totalStakeMax": 10000,
                "stakeAll": [10, 30, 50, 100],
                defaultTotalStake: 500000,
                stakeMax: 100,
                stakeMin: 10
            },
            [NewLimitsConfigurationSpec.lowLevel.title]: {
                "bets": {
                    "big": { "exposure": 40000, "max": 250, "min": 1, "payout": 1.54 },
                    "egalite": { "exposure": 555000, "max": 25, "min": 1, "payout": 221 },
                    "playerPair": { "exposure": 550000, "max": 500, "min": 1, "payout": 11 }
                },
                "concurrentPlayers": 100,
                "totalStakeMin": 50,
                "stakeDef": 1,
                "totalStakeMax": 2500,
                "stakeAll": [1, 3, 5, 10],
                defaultTotalStake: 1000,
                stakeMax: 10,
                stakeMin: 1,
                "isDefaultRoom": true
            }
        });
    }

    @test()
    public async getFixedStakeAllDefaultCurrency() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brand);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.slotDefinitionIds[4], 15),
            "EUR");
        expect(limits).deep.equal({
            defaultTotalStake: 1.5,
            customParam: 2,
            maxTotalStake: 6,
            stakeAll: [0.04, 0.1, 0.2, 0.4],
            stakeDef: 0.1,
            stakeMax: 0.4,
            stakeMin: 0.04,
            winMax: 500000
        });
    }

    @test()
    public async getFixedStakeAllCNY() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brand);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.slotDefinitionIds[4], 15),
            "CNY");
        expect(limits).deep.equal({
            defaultTotalStake: 15,
            customParam: 20,
            maxTotalStake: 60,
            stakeAll: [0.4, 1, 2, 4],
            stakeDef: 1,
            stakeMax: 4,
            stakeMin: 0.4,
            winMax: 3982562.13
        });
    }

    private createGame(schemaDefinitionId: number, totalBetMultiplier: number, gameCode?: string): Game {
        return {
            totalBetMultiplier,
            schemaDefinitionId,
            code: gameCode
        } as any;
    }

    @test()
    public async getConstForAllCurrenciesStakeAllRUP() {

        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brand);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.slotDefinitionIds[5], 10),
            "RUP");
        expect(limits).deep.equal({
            maxTotalStake: 8,
            stakeAll: [0.03, 0.6, 0.7, 0.8],
            stakeDef: 0.8,
            stakeMax: 0.8,
            stakeMin: 0.03,
            winMax: 211.248
        });
    }

    @test()
    public async getConstForAllCurrenciesStakeAllRUPWithSets() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brand);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.slotDefinitionIds[6], 10, "testGame3"),
            "RUP");
        expect(limits).deep.equal({
            [NewLimitsConfigurationSpec.highLevel.title]: {
                "bets": {
                    "big": { "exposure": 770000, "max": 5000, "min": 50, "payout": 1.54 },
                    "egalite": { "exposure": 11050000, "max": 500, "min": 50, "payout": 221 },
                    "playerPair": { "exposure": 12000000, "max": 10000, "min": 50, "payout": 12 }
                },
                "concurrentPlayers": 100,
                "defaultTotalStake": 10,
                stakeAll: [1, 3, 5, 10, 25],
                stakeDef: 100,
                stakeMax: 25,
                stakeMin: 1,
                totalStakeMax: 100000,
                totalStakeMin: 50
            },
            [NewLimitsConfigurationSpec.lowLevel.title]: {
                "bets": {
                    "big": { "exposure": 385000, "max": 2500, "min": 10, "payout": 1.54 },
                    "egalite": { "exposure": 5525000, "max": 250, "min": 10, "payout": 221 },
                    "playerPair": { "exposure": 6000000, "max": 5000, "min": 10, "payout": 12 }
                },
                "concurrentPlayers": 100,
                "defaultTotalStake": 10,
                stakeAll: [1, 3, 5, 10],
                stakeDef: 10,
                stakeMax: 10,
                stakeMin: 1,
                totalStakeMax: 25000,
                totalStakeMin: 10
            }
        });
    }

    @test()
    public async smartRoundingSimple() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brand);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.definitionWithSmartRounding.id, 1),
            "PLN");
        expect(limits).deep.equal({
            "maxTotalStake": 6500,
            "stakeAll": [0.05, 0.1, 0.2, 0.3],
            "stakeDef": 0.3,
            "winMax": 2146484.33
        });
    }

    @test()
    public async getBacLimitsSmartRounding() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brand);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.slotDefinitionIds[3], undefined, "testGame1"),
            "PLN");
        delete limits[NewLimitsConfigurationSpec.lowLevel.title].stakeAll;
        delete limits[NewLimitsConfigurationSpec.highLevel.title].stakeAll;
        expect(limits).deep.equal({
            [NewLimitsConfigurationSpec.lowLevel.title]: {
                "bets": {
                    "big": { "exposure": 195000, "max": 1250, "min": 5, "payout": 1.54 },
                    "egalite": { "exposure": 2765000, "max": 125, "min": 5, "payout": 221 },
                    "playerPair": { "exposure": 3000000, "max": 2500, "min": 5, "payout": 12 }
                },
                "concurrentPlayers": 100,
                "stakeDef": 5,
                "stakeMax": 500,
                "stakeMin": 5,
                defaultTotalStake: 5,
                "totalStakeMax": 15000,
                "totalStakeMin": 5,
                "isDefaultRoom": true
            },
            [NewLimitsConfigurationSpec.highLevel.title]: {
                "bets": {
                    "big": { "exposure": 385000, "max": 2500, "min": 2500, "payout": 1.54 },
                    "egalite": { "exposure": 5525000000, "max": 250000, "min": 2500, "payout": 221 },
                    "playerPair": { "exposure": 540000000, "max": 450000, "min": 2500, "payout": 12 }
                },
                "concurrentPlayers": 100,
                "stakeDef": 50,
                "stakeMax": 50000,
                "stakeMin": 2500,
                defaultTotalStake: 2500,
                "totalStakeMax": 50000,
                "totalStakeMin": 25
            }
        });
    }

    @test()
    public async smartRoundingSimpleFirstCurrencyGroupShouldntBeConverted() {
        const service = getNewLimitsFacade(NewLimitsConfigurationSpec.brand);
        const limits = await service.buildGameLaunch(
            this.createGame(NewLimitsConfigurationSpec.definitionWithSmartRounding.id, 1),
            "USD");
        expect(limits).deep.equal({
            "maxTotalStake": 1250,
            "stakeAll": [0.01, 0.02, 0.03, 0.05],
            "stakeDef": 0.05,
            "winMax": 573236.72
        });
    }
}
