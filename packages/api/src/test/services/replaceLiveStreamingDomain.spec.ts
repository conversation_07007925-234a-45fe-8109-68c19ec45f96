import { expect, use } from "chai";
import * as sinon from "sinon";
import * as sinon<PERSON>hai from "sinon-chai";
import { BrandEntity } from "../../skywind/entities/brand";
import { BrandClientFeatures } from "../../skywind/entities/game";
import { StaticDomain, StaticDomainType } from "../../skywind/entities/domain";
import * as entityStaticDomainService from "../../skywind/services/entityStaticDomainService";

use(sinonChai);

// Import the function to test after setting up mocks
let replaceLiveStreamingDomain: any;

describe("replaceLiveStreamingDomain", () => {
    let mockEntityStaticDomainService: sinon.SinonStubbedInstance<any>;
    let mockBrand: BrandEntity;
    let mockDomain: StaticDomain;
    let getEntityStaticDomainServiceStub: sinon.SinonStub;

    beforeEach(() => {
        // Create mock brand entity
        mockBrand = {
            id: 1,
            name: "Test Brand"
        } as BrandEnti<PERSON>;

        // Create mock domain
        mockDomain = {
            id: 1,
            domain: "live-streaming.example.com",
            type: StaticDomainType.LIVE_STREAMING
        } as StaticDomain;

        // Mock the entity static domain service
        mockEntityStaticDomainService = {
            pick: sinon.stub()
        };

        // Stub the service getter
        getEntityStaticDomainServiceStub = sinon.stub(entityStaticDomainService, "getEntityStaticDomainService")
            .returns(mockEntityStaticDomainService);

        // Import the function after mocking
        replaceLiveStreamingDomain = require("../../skywind/services/playService").replaceLiveStreamingDomain;
    });

    afterEach(() => {
        sinon.restore();
    });

    describe("when clientFeatures contains fields with {liveStreamingDomain} placeholder", () => {
        it("should replace {liveStreamingDomain} in streamCommonUrl", async () => {
            // Arrange
            const clientFeatures: BrandClientFeatures = {
                streamCommonUrl: "https://{liveStreamingDomain}/stream/common",
                otherField: "no placeholder here"
            };
            mockEntityStaticDomainService.pick.resolves(mockDomain);

            // Act
            await replaceLiveStreamingDomain(clientFeatures, mockBrand);

            // Assert
            expect(clientFeatures.streamCommonUrl).to.equal("https://live-streaming.example.com/stream/common");
            expect(clientFeatures.otherField).to.equal("no placeholder here");
            expect(mockEntityStaticDomainService.pick).to.have.been.calledWith(mockBrand, StaticDomainType.LIVE_STREAMING);
            expect(mockEntityStaticDomainService.pick).to.have.been.calledOnce;
        });

        it("should replace {liveStreamingDomain} in streamWebsocket", async () => {
            // Arrange
            const clientFeatures: BrandClientFeatures = {
                streamWebsocket: "wss://{liveStreamingDomain}/websocket"
            };
            mockEntityStaticDomainService.pick.resolves(mockDomain);

            // Act
            await replaceLiveStreamingDomain(clientFeatures, mockBrand);

            // Assert
            expect(clientFeatures.streamWebsocket).to.equal("wss://live-streaming.example.com/websocket");
        });

        it("should replace {liveStreamingDomain} in streamHls", async () => {
            // Arrange
            const clientFeatures: BrandClientFeatures = {
                streamHls: "https://{liveStreamingDomain}/hls/stream.m3u8"
            };
            mockEntityStaticDomainService.pick.resolves(mockDomain);

            // Act
            await replaceLiveStreamingDomain(clientFeatures, mockBrand);

            // Assert
            expect(clientFeatures.streamHls).to.equal("https://live-streaming.example.com/hls/stream.m3u8");
        });

        it("should replace {liveStreamingDomain} in streamProgressive", async () => {
            // Arrange
            const clientFeatures: BrandClientFeatures = {
                streamProgressive: "https://{liveStreamingDomain}/progressive/stream.mp4"
            };
            mockEntityStaticDomainService.pick.resolves(mockDomain);

            // Act
            await replaceLiveStreamingDomain(clientFeatures, mockBrand);

            // Assert
            expect(clientFeatures.streamProgressive).to.equal("https://live-streaming.example.com/progressive/stream.mp4");
        });

        it("should replace {liveStreamingDomain} in multiple fields", async () => {
            // Arrange
            const clientFeatures: BrandClientFeatures = {
                streamCommonUrl: "https://{liveStreamingDomain}/common",
                streamWebsocket: "wss://{liveStreamingDomain}/ws",
                streamHls: "https://{liveStreamingDomain}/hls",
                streamProgressive: "https://{liveStreamingDomain}/progressive",
                otherField: "no placeholder"
            };
            mockEntityStaticDomainService.pick.resolves(mockDomain);

            // Act
            await replaceLiveStreamingDomain(clientFeatures, mockBrand);

            // Assert
            expect(clientFeatures.streamCommonUrl).to.equal("https://live-streaming.example.com/common");
            expect(clientFeatures.streamWebsocket).to.equal("wss://live-streaming.example.com/ws");
            expect(clientFeatures.streamHls).to.equal("https://live-streaming.example.com/hls");
            expect(clientFeatures.streamProgressive).to.equal("https://live-streaming.example.com/progressive");
            expect(clientFeatures.otherField).to.equal("no placeholder");
            expect(mockEntityStaticDomainService.pick).to.have.been.calledWith(mockBrand, StaticDomainType.LIVE_STREAMING);
            expect(mockEntityStaticDomainService.pick).to.have.been.calledOnce;
        });

        it("should replace multiple occurrences of {liveStreamingDomain} in the same field", async () => {
            // Arrange
            const clientFeatures: BrandClientFeatures = {
                streamCommonUrl: "https://{liveStreamingDomain}/path?redirect={liveStreamingDomain}"
            };
            mockEntityStaticDomainService.pick.resolves(mockDomain);

            // Act
            await replaceLiveStreamingDomain(clientFeatures, mockBrand);

            // Assert
            expect(clientFeatures.streamCommonUrl).to.equal("https://live-streaming.example.com/path?redirect=live-streaming.example.com");
        });
    });

    describe("when clientFeatures does not contain fields with {liveStreamingDomain} placeholder", () => {
        it("should not call domain service when no stream fields exist", async () => {
            // Arrange
            const clientFeatures: BrandClientFeatures = {
                otherField: "no stream fields here",
                anotherField: "also no placeholders"
            };

            // Act
            await replaceLiveStreamingDomain(clientFeatures, mockBrand);

            // Assert
            expect(mockEntityStaticDomainService.pick).to.not.have.been.called;
            expect(clientFeatures.otherField).to.equal("no stream fields here");
            expect(clientFeatures.anotherField).to.equal("also no placeholders");
        });

        it("should not call domain service when stream fields exist but have no placeholder", async () => {
            // Arrange
            const clientFeatures: BrandClientFeatures = {
                streamCommonUrl: "https://example.com/stream",
                streamWebsocket: "wss://example.com/ws",
                streamHls: "https://example.com/hls",
                streamProgressive: "https://example.com/progressive"
            };

            // Act
            await replaceLiveStreamingDomain(clientFeatures, mockBrand);

            // Assert
            expect(mockEntityStaticDomainService.pick).to.not.have.been.called;
            expect(clientFeatures.streamCommonUrl).to.equal("https://example.com/stream");
            expect(clientFeatures.streamWebsocket).to.equal("wss://example.com/ws");
            expect(clientFeatures.streamHls).to.equal("https://example.com/hls");
            expect(clientFeatures.streamProgressive).to.equal("https://example.com/progressive");
        });
    });

    describe("when domain service returns null or undefined domain", () => {
        it("should not modify fields when domain service returns null", async () => {
            // Arrange
            const clientFeatures: BrandClientFeatures = {
                streamCommonUrl: "https://{liveStreamingDomain}/stream"
            };
            mockEntityStaticDomainService.pick.resolves(null);

            // Act
            await replaceLiveStreamingDomain(clientFeatures, mockBrand);

            // Assert
            expect(clientFeatures.streamCommonUrl).to.equal("https://{liveStreamingDomain}/stream");
            expect(mockEntityStaticDomainService.pick).to.have.been.calledWith(mockBrand, StaticDomainType.LIVE_STREAMING);
            expect(mockEntityStaticDomainService.pick).to.have.been.calledOnce;
        });

        it("should not modify fields when domain service returns domain without domain property", async () => {
            // Arrange
            const clientFeatures: BrandClientFeatures = {
                streamCommonUrl: "https://{liveStreamingDomain}/stream"
            };
            const domainWithoutDomainProperty = {
                id: 1,
                type: StaticDomainType.LIVE_STREAMING
            } as StaticDomain;
            mockEntityStaticDomainService.pick.resolves(domainWithoutDomainProperty);

            // Act
            await replaceLiveStreamingDomain(clientFeatures, mockBrand);

            // Assert
            expect(clientFeatures.streamCommonUrl).to.equal("https://{liveStreamingDomain}/stream");
        });
    });

    describe("edge cases", () => {
        it("should handle empty clientFeatures object", async () => {
            // Arrange
            const clientFeatures: BrandClientFeatures = {};

            // Act
            await replaceLiveStreamingDomain(clientFeatures, mockBrand);

            // Assert
            expect(mockEntityStaticDomainService.pick).to.not.have.been.called;
        });

        it("should handle null/undefined field values", async () => {
            // Arrange
            const clientFeatures: BrandClientFeatures = {
                streamCommonUrl: null,
                streamWebsocket: undefined,
                streamHls: "",
                streamProgressive: "https://{liveStreamingDomain}/stream"
            };
            mockEntityStaticDomainService.pick.resolves(mockDomain);

            // Act
            await replaceLiveStreamingDomain(clientFeatures, mockBrand);

            // Assert
            expect(clientFeatures.streamProgressive).to.equal("https://live-streaming.example.com/stream");
            expect(clientFeatures.streamCommonUrl).to.be.null;
            expect(clientFeatures.streamWebsocket).to.be.undefined;
            expect(clientFeatures.streamHls).to.equal("");
        });
    });
});
