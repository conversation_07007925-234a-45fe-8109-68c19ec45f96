import { suite, test, timeout } from "mocha-typescript";
import { expect, should, use } from "chai";
import { SinonStub, stub, restore } from "sinon";
import { truncate } from "../entities/helper";
import { factory } from "factory-girl";
import { FACTORY } from "../factories/common";
import {
    BULK_OPERATION_ACTION,
    ENTITY_BULK_OPERATION_TYPE,
    EntityBulkOperation
} from "../../skywind/entities/bulk";
import { EntityBulkService } from "../../skywind/services/bulk/entityBulkService";
import { MIGRATION_STATUS } from "../../skywind/entities/entity";
import { Models } from "../../skywind/models/models";
import MigrationService from "../../skywind/services/migrationService";
import config from "../../skywind/config";
import { getDynamicDomainService, getStaticDomainService } from "../../skywind/services/domain";

const chaiAsPromised = require("chai-as-promised");

@suite(timeout(30000))
class EntityBulkServiceCrossGsSpec {
    public service: EntityBulkService = new EntityBulkService();
    public migrationServiceStub: SinonStub;
    public entityModelStub: SinonStub;
    public dynamicDomainServiceStub: SinonStub;
    public staticDomainServiceStub: SinonStub;

    public static async before() {
        should();
        use(chaiAsPromised);
        await truncate();
    }

    public before() {
        this.migrationServiceStub = stub(MigrationService, "startMigration").resolves();
        this.entityModelStub = stub(Models.EntityModel, "update").callThrough();
        
        // Stub domain services to return the domains that are created in tests
        this.dynamicDomainServiceStub = stub(getDynamicDomainService(), "findAll").callsFake(async (options) => {
            // Return all created domains for validation
            const domains = await Models.DynamicDomainModel.findAll(options);
            return domains.map(d => d.toInfo());
        });
        
        this.staticDomainServiceStub = stub(getStaticDomainService(), "findAll").callsFake(async (options) => {
            const domains = await Models.StaticDomainModel.findAll(options);
            return domains.map(d => d.toInfo());
        });
    }

    public after() {
        this.migrationServiceStub.restore();
        this.entityModelStub.restore();
        this.dynamicDomainServiceStub.restore();
        this.staticDomainServiceStub.restore();
        restore();
    }

    @test()
    public async "should process cross-GS migration operations in batches"() {
        const oldBatchSize = config.migration.crossGsMigrationBatchSize;
        config.migration.crossGsMigrationBatchSize = 2; // Small batch size for testing

        try {
            const staticDomain = await factory.create(FACTORY.STATIC_DOMAIN);
            const dynamicDomain1 = await factory.create(FACTORY.DYNAMIC_DOMAIN, { environment: "env1" });
            const dynamicDomain2 = await factory.create(FACTORY.DYNAMIC_DOMAIN, { environment: "env2" });

            const parent = await factory.create(FACTORY.ENTITY);
            const child1 = await factory.create(FACTORY.ENTITY, {}, { parent, dynamicDomainId: dynamicDomain1.id, environment: "env1" });
            const child2 = await factory.create(FACTORY.ENTITY, {}, { parent, dynamicDomainId: dynamicDomain1.id, environment: "env1" });
            const child3 = await factory.create(FACTORY.ENTITY, {}, { parent, dynamicDomainId: dynamicDomain1.id, environment: "env1" });

            // Create operations that require cross-GS migration (more than batch size)
            const operations: EntityBulkOperation[] = [
                {
                    entityKey: child1.key,
                    action: BULK_OPERATION_ACTION.SET,
                    item: { id: dynamicDomain2.id, type: ENTITY_BULK_OPERATION_TYPE.DYNAMIC }
                },
                {
                    entityKey: child2.key,
                    action: BULK_OPERATION_ACTION.SET,
                    item: { id: dynamicDomain2.id, type: ENTITY_BULK_OPERATION_TYPE.DYNAMIC }
                },
                {
                    entityKey: child3.key,
                    action: BULK_OPERATION_ACTION.SET,
                    item: { id: dynamicDomain2.id, type: ENTITY_BULK_OPERATION_TYPE.DYNAMIC }
                }
            ];

            const results = await this.service.process(parent, operations);

            expect(results).to.have.length(3);
            // Verify that batching was used (operations.length > batchSize)
            expect(operations.length > config.migration.crossGsMigrationBatchSize).to.be.true;
        } finally {
            config.migration.crossGsMigrationBatchSize = oldBatchSize;
        }
    }

    @test()
    public async "should handle cross-GS migration when environments differ"() {
        const dynamicDomain1 = await factory.create(FACTORY.DYNAMIC_DOMAIN, { environment: "env1" });
        const dynamicDomain2 = await factory.create(FACTORY.DYNAMIC_DOMAIN, { environment: "env2" });

        const parent = await factory.create(FACTORY.ENTITY);
        const child = await factory.create(FACTORY.ENTITY, {}, { 
            parent, 
            dynamicDomainId: dynamicDomain1.id, 
            environment: "env1" 
        });

        const operations: EntityBulkOperation[] = [
            {
                entityKey: child.key,
                action: BULK_OPERATION_ACTION.SET,
                item: { id: dynamicDomain2.id, type: ENTITY_BULK_OPERATION_TYPE.DYNAMIC }
            }
        ];

        const results = await this.service.process(parent, operations);

        expect(results).to.have.length(1);
        // Check if EntityModel.update was called with migration parameters
        const migrationCalls = this.entityModelStub.getCalls().filter(call => 
            call.args[0] && call.args[0].migrationStatus
        );
        expect(migrationCalls).to.have.length(1);
        
        // Verify the update was called with migration status
        expect(migrationCalls[0].args[0]).to.deep.include({
            migrationStatus: MIGRATION_STATUS.STARTED,
            prevDynamicDomainId: dynamicDomain1.id
        });
    }

    @test()
    public async "should not trigger migration when environments match"() {
        const dynamicDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, { environment: "env1" });
        const sameEnvDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, { environment: "env1" });

        const parent = await factory.create(FACTORY.ENTITY);
        const child = await factory.create(FACTORY.ENTITY, {}, { 
            parent, 
            dynamicDomainId: dynamicDomain.id, 
            environment: "env1" 
        });

        const operations: EntityBulkOperation[] = [
            {
                entityKey: child.key,
                action: BULK_OPERATION_ACTION.SET,
                item: { id: sameEnvDomain.id, type: ENTITY_BULK_OPERATION_TYPE.DYNAMIC }
            }
        ];

        const results = await this.service.process(parent, operations);

        expect(results).to.have.length(1);
        // Check that no migration-related calls were made (calls with migrationStatus)
        const migrationCalls = this.entityModelStub.getCalls().filter(call => 
            call.args[0] && call.args[0].migrationStatus
        );
        expect(migrationCalls).to.have.length(0);
    }

    @test()
    public async "should handle unset operation with environment change"() {
        const dynamicDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, { environment: "env1" });
        const parentDynamicDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, { environment: "env2" });

        const parent = await factory.create(FACTORY.ENTITY, {}, { 
            dynamicDomainId: parentDynamicDomain.id, 
            environment: "env2" 
        });
        const child = await factory.create(FACTORY.ENTITY, {}, { 
            parent, 
            dynamicDomainId: dynamicDomain.id, 
            environment: "env1" 
        });

        const operations: EntityBulkOperation[] = [
            {
                entityKey: child.key,
                action: BULK_OPERATION_ACTION.RESET,
                item: { type: ENTITY_BULK_OPERATION_TYPE.DYNAMIC }
            }
        ];

        const results = await this.service.process(parent, operations);

        expect(results).to.have.length(1);
        // Check if EntityModel.update was called with migration parameters
        const migrationCalls = this.entityModelStub.getCalls().filter(call => 
            call.args[0] && call.args[0].migrationStatus
        );
        expect(migrationCalls).to.have.length(1);
        
        // Verify the update was called with migration status and the child's original domain ID
        expect(migrationCalls[0].args[0]).to.deep.include({
            migrationStatus: MIGRATION_STATUS.STARTED,
            prevDynamicDomainId: dynamicDomain.id // Should be the child's original domain before reset
        });
    }

    @test()
    public async "should use normal processing for small operation sets"() {
        const oldBatchSize = config.migration.crossGsMigrationBatchSize;
        config.migration.crossGsMigrationBatchSize = 100; // Large batch size

        try {
            const dynamicDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, { environment: "env1" });
            const parent = await factory.create(FACTORY.ENTITY);
            const child = await factory.create(FACTORY.ENTITY, {}, { parent });

            const operations: EntityBulkOperation[] = [
                {
                    entityKey: child.key,
                    action: BULK_OPERATION_ACTION.SET,
                    item: { id: dynamicDomain.id, type: ENTITY_BULK_OPERATION_TYPE.DYNAMIC }
                }
            ];

            const results = await this.service.process(parent, operations);

            expect(results).to.have.length(1);
            // Should use normal processing path, not cross-GS batching
        } finally {
            config.migration.crossGsMigrationBatchSize = oldBatchSize;
        }
    }

    @test()
    public async "should handle mixed operation types in batches"() {
        const oldBatchSize = config.migration.crossGsMigrationBatchSize;
        config.migration.crossGsMigrationBatchSize = 2; // Small batch size for testing

        try {
            const staticDomain = await factory.create(FACTORY.STATIC_DOMAIN);
            const dynamicDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, { environment: "env1" });
            const parent = await factory.create(FACTORY.ENTITY);
            const child1 = await factory.create(FACTORY.ENTITY, {}, { parent });
            const child2 = await factory.create(FACTORY.ENTITY, {}, { parent });
            const child3 = await factory.create(FACTORY.ENTITY, {}, { parent });

            const operations: EntityBulkOperation[] = [
                {
                    entityKey: child1.key,
                    action: BULK_OPERATION_ACTION.SET,
                    item: { id: staticDomain.id, type: ENTITY_BULK_OPERATION_TYPE.STATIC }
                },
                {
                    entityKey: child2.key,
                    action: BULK_OPERATION_ACTION.SET,
                    item: { id: dynamicDomain.id, type: ENTITY_BULK_OPERATION_TYPE.DYNAMIC }
                },
                {
                    entityKey: child3.key,
                    action: BULK_OPERATION_ACTION.SET,
                    item: { id: staticDomain.id, type: ENTITY_BULK_OPERATION_TYPE.STATIC }
                }
            ];

            const results = await this.service.process(parent, operations);

            expect(results).to.have.length(3);
        } finally {
            config.migration.crossGsMigrationBatchSize = oldBatchSize;
        }
    }
}