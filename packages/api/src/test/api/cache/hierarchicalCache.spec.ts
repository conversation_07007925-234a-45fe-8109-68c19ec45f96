import { expect } from "chai";
import { HierarchicalCache } from "../../../skywind/cache/hierarchicalCache";

describe("HierarchicalCache", () => {
    let cache: HierarchicalCache<string, any>;

    beforeEach(() => {
        // Create cache instance with actual Redis connections
        cache = new HierarchicalCache(
            "test-cache",
            (id: string, ...args: any[]) => ({ value: "test-data" }),
            {
                stdTTL: 60,
                checkperiod: 10
            }
        );
    });

    afterEach(async () => {
        // Clean up cache and close Redis connections
        if (cache) {
            cache.reset();
            // Give Redis connections time to close
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    });

    it("should initialize with correct properties", () => {
        expect(cache).to.be.instanceOf(HierarchicalCache);
        expect(cache.keys).to.be.a("function");
    });

    it("should find and cache value when connected", async () => {
        // Set cache as connected
        (cache as any).connected = true;
        
        const result = await cache.find("entity1:reseller", "key1", "key2");
        
        expect(result).to.deep.equal({ value: "test-data" });
    });

    it("should bypass cache when not connected", async () => {
        // Cache is not connected by default (connected = false)
        const result = await cache.find("entity1:reseller", "key1");
        
        // Should get result from search function, not cache
        expect(result).to.deep.equal({ value: "test-data" });
    });

    it("should reset specific cache entry", () => {
        // Set up cache entry
        (cache as any).cache.set("entity1:reseller", { value: "test" });
        (cache as any).cacheTree.addKey("entity1:reseller");
        
        cache.reset("entity1:reseller");
        
        expect((cache as any).cache.get("entity1:reseller")).to.be.undefined;
    });

    it("should reset all cache entries", () => {
        // Set up cache entries
        (cache as any).cache.set("entity1:reseller", { value: "test1" });
        (cache as any).cache.set("entity2:reseller", { value: "test2" });
        (cache as any).cacheTree.addKey("entity1:reseller");
        (cache as any).cacheTree.addKey("entity2:reseller");
        
        cache.reset();
        
        expect((cache as any).cache.keys()).to.have.lengthOf(0);
    });

    it("should reset hierarchy correctly", () => {
        // Set up cache entries
        const entries = [
            "entity1:reseller",
            "entity1:reseller:merchant1",
            "entity1:reseller:merchant2",
            "entity2:reseller:merchant3"
        ];
        
        entries.forEach(entry => {
            (cache as any).cache.set(entry, { value: entry });
            (cache as any).cacheTree.addKey(entry);
        });
        
        // Reset hierarchy for entity1:reseller
        cache.reset("entity1:reseller");
        
        // Check that only entity1:reseller entries were removed
        expect((cache as any).cache.get("entity1:reseller")).to.be.undefined;
        expect((cache as any).cache.get("entity1:reseller:merchant1")).to.be.undefined;
        expect((cache as any).cache.get("entity1:reseller:merchant2")).to.be.undefined;
        expect((cache as any).cache.get("entity2:reseller:merchant3")).to.exist;
    });

    it("should reset hierarchy for leaf nodes", () => {
        // Set up cache entries
        const entries = [
            "entity1:reseller",
            "entity1:reseller:merchant1",
            "entity1:reseller:merchant2"
        ];
        
        entries.forEach(entry => {
            (cache as any).cache.set(entry, { value: entry });
            (cache as any).cacheTree.addKey(entry);
        });
        
        // Reset hierarchy for leaf node
        cache.reset("entity1:reseller:merchant1");
        
        // Check that only the leaf node was removed
        expect((cache as any).cache.get("entity1:reseller")).to.exist;
        expect((cache as any).cache.get("entity1:reseller:merchant1")).to.be.undefined;
        expect((cache as any).cache.get("entity1:reseller:merchant2")).to.exist;
    });

    it("should handle complex hierarchy structure", () => {
        const entries = [
            "root",
            "root:level1a",
            "root:level1a:level2a",
            "root:level1a:level2b",
            "root:level1b",
            "root:level1b:level2c"
        ];
        
        entries.forEach(entry => {
            (cache as any).cache.set(entry, { value: entry });
            (cache as any).cacheTree.addKey(entry);
        });
        
        // Reset root:level1a hierarchy
        cache.reset("root:level1a");
        
        // Check removals
        expect((cache as any).cache.get("root:level1a")).to.be.undefined;
        expect((cache as any).cache.get("root:level1a:level2a")).to.be.undefined;
        expect((cache as any).cache.get("root:level1a:level2b")).to.be.undefined;
        
        // Check remaining entries
        expect((cache as any).cache.get("root")).to.exist;
        expect((cache as any).cache.get("root:level1b")).to.exist;
        expect((cache as any).cache.get("root:level1b:level2c")).to.exist;
    });

    it("should return all cache keys", () => {
        const entries = ["entity1:reseller", "entity1:reseller:merchant1"];
        
        entries.forEach(entry => {
            (cache as any).cache.set(entry, { value: entry });
        });
        
        const keys = cache.keys();
        expect(keys).to.have.lengthOf(2);
        expect(keys).to.include("entity1:reseller");
        expect(keys).to.include("entity1:reseller:merchant1");
    });

    it("should handle search function errors", async () => {
        // Test using the existing cache but with disconnected state
        // Override the search function temporarily
        const originalSearch = (cache as any).search;
        (cache as any).search = () => { throw new Error("Search failed"); };
        
        try {
            await cache.find("entity1:reseller", "key1");
            expect.fail("Should have thrown an error");
        } catch (error) {
            expect(error.message).to.equal("Search failed");
        } finally {
            // Restore original search function
            (cache as any).search = originalSearch;
        }
    });

    it("should handle Redis connection state", async () => {
        // Allow some time for Redis connection to be established
        await new Promise(resolve => setTimeout(resolve, 200));
        
        // After connection, the cache should be connected
        // Note: This test depends on actual Redis being available
        // If Redis is not available, the cache will remain disconnected
        const connected = (cache as any).connected;
        expect(connected).to.be.a("boolean");
    });

    it("should handle master entity invalidation with \":\" prefix", () => {
        // Set up cache entries with various entity paths
        const entries = [
            "entity1:reseller",
            "entity1:reseller:merchant1",
            "entity2:reseller",
            "entity2:reseller:merchant2",
            "entity3:brand"
        ];
        
        entries.forEach(entry => {
            (cache as any).cache.set(entry, { value: entry });
            (cache as any).cacheTree.addKey(entry);
        });
        
        // Reset hierarchy for master entity (just ":")
        cache.reset(":");
        
        // All entries should be removed since ":" represents the master/root entity
        expect((cache as any).cache.keys()).to.have.lengthOf(0);
        expect((cache as any).cacheTree.getKeysWithPrefix("")).to.have.lengthOf(0);
    });

    it("should handle empty prefix in CacheTree getKeysWithPrefix", () => {
        // Set up cache entries
        const entries = [
            "entity1:reseller",
            "entity1:reseller:merchant1",
            "entity2:reseller"
        ];
        
        entries.forEach(entry => {
            (cache as any).cacheTree.addKey(entry);
        });
        
        // Empty prefix should return all keys
        const allKeys = (cache as any).cacheTree.getKeysWithPrefix("");
        expect(allKeys).to.have.lengthOf(3);
        expect(allKeys).to.include.members(entries);
    });

    it("should handle \":\" prefix in CacheTree getKeysWithPrefix", () => {
        // Set up cache entries
        const entries = [
            "entity1:reseller", 
            "entity1:reseller:merchant1",
            "entity2:reseller"
        ];
        
        entries.forEach(entry => {
            (cache as any).cacheTree.addKey(entry);
        });
        
        // ":" prefix should return all keys (master entity case)
        const allKeys = (cache as any).cacheTree.getKeysWithPrefix(":");
        expect(allKeys).to.have.lengthOf(3);
        expect(allKeys).to.include.members(entries);
    });

});
