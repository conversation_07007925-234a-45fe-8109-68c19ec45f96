import { expect, should, use } from "chai";
import * as chaiAsPromised from "chai-as-promised";
import { suite, test } from "mocha-typescript";
import { getGameLimitsCurrency } from "../../../skywind/cache/gameLimitsCurrencies";
import { GameLimitsCurrency } from "@skywind-group/sw-currency-exchange";
import { RedisCache } from "../../../skywind/cache/redisCache";

class MockCache extends RedisCache<string, GameLimitsCurrency> {

    constructor(fn: (key: string, currency: string, version: number) => GameLimitsCurrency) {
        super("game-limits-currencies", fn);
    }

    public query(currency: string, version: number) {
        return super.find(`${currency}_${version}`, currency, version);
    }
}

const cache = new MockCache(() => ({
    toEURMultiplier: 1.5,
    copyLimitsFrom: "EUR"
}));

@suite("GameLimitsCurrencies")
class GameLimitsCurrenciesTests {

    public static before() {
        should();
        use(chaiAsPromised);
    }

    @test
    public async "should return multiplier and copyLimitsFrom from cache"() {
        const result = await getGameLimitsCurrency(
            "USD",
            undefined,
            {
                gameLimitsCurrenciesVersion: 1,
                gameLimitsSettings: {}
            },
            cache
        );

        expect(result).to.deep.equal({
            toEURMultiplier: 1.5,
            copyLimitsFrom: "EUR"
        });
    }

    @test
    public async "should use settings from entitySettings when provided"() {
        const result = await getGameLimitsCurrency(
            "USD",
            {
                gameLimitsSettings: {
                    USD: {
                        toEURMultiplier: 2.0,
                        copyLimitsFrom: "GBP"
                    }
                },
                emailTemplates: undefined
            },
            {
                gameLimitsCurrenciesVersion: 1,
                gameLimitsSettings: {
                    USD: {
                        copyLimitsFrom: "XXX"
                    }
                }
            },
            cache
        );

        expect(result).to.have.property("toEURMultiplier", 2.0);
        expect(result).to.have.property("copyLimitsFrom", "GBP");
    }

    @test
    public async "should use settings from game features when provided"() {
        const result = await getGameLimitsCurrency(
            "USD",
            {
                emailTemplates: undefined
            },
            {
                gameLimitsCurrenciesVersion: 1,
                gameLimitsSettings: {
                    USD: {
                        copyLimitsFrom: "XXX"
                    }
                }
            },
            cache
        );

        expect(result).to.have.property("toEURMultiplier", 1.5);
        expect(result).to.have.property("copyLimitsFrom", "XXX");
    }

    @test
    public async "should fallback to DEFAULT_VERSION when multiplier is missing"() {
        const result = await getGameLimitsCurrency(
            "USD",
            undefined,
            {
                gameLimitsCurrenciesVersion: 2,
                gameLimitsSettings: {}
            },
            new MockCache((key, currency, version) => {
                if (currency === "USD") {
                    if (version === 2) {
                        return { copyLimitsFrom: "EUR" };
                    }
                    if (version === 1) {
                        return { toEURMultiplier: 1.5, copyLimitsFrom: "EUR" };
                    }
                }
            })
        );

        expect(result).to.exist;
        expect(result.toEURMultiplier).to.equal(1.5);
        expect(result.copyLimitsFrom).to.equal("EUR");
    }

    @test
    public async "should throw error when currency exchange service fails"() {
        const result = await getGameLimitsCurrency(
            "USD",
            undefined,
            {
                gameLimitsCurrenciesVersion: 1,
                gameLimitsSettings: {}
            },
            new MockCache(() => {
                throw new Error("Service unavailable");
            })
        );

        expect(result).to.exist;
        expect(result.toEURMultiplier).to.be.undefined;
        expect(result.copyLimitsFrom).to.be.undefined;
    }
}
