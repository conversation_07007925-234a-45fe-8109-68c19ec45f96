import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { Application } from "express";
import { createComplexStructure, truncate,
    withTransactionDecorator } from "../entities/helper";
import { application } from "../../skywind/server";
import * as RoleService from "../../skywind/services/role";
import getUserService, { initDB } from "../../skywind/services/user/user";
import { CreateDataBody } from "../../skywind/entities/gameServerSettings";
import { GameServerSettingsService, getGameServerSettingsService } from "../../skywind/services/gameServerSettings";

const request = require("supertest");
const uuid = require("uuid");

@suite("GS settings API")
class GSSettingsSpec {
    public static server: Application;
    public static token: string;

    public service: GameServerSettingsService = getGameServerSettingsService();

    public static makeLogin(username: string, password: string, key: string): Promise<string> {
        return request(GSSettingsSpec.server)
            .post("/v1/login")
            .send({
                secretKey: key,
                username: username,
                password: password,
            })
            .then((response) => {
                expect(response.status).to.be.equal(200);
                GSSettingsSpec.token = response.body.accessToken;
            });
    }

    public static async before() {
        GSSettingsSpec.server = await application.get();

        await truncate();

        const master = await createComplexStructure();
        await initDB();

        const role = await RoleService.createRole({
            title: "AUDITOR",
            permissions: [
                "gs:settings:view",
                "gs:settings:create",
                "gs:settings:remove",
                "gs:settings:edit",
                "gs:settings"
            ],
            entityId: master.id,
            isShared: false
        });
        const username = "MisterCheburek";
        const password = uuid.v4();
        await getUserService(master).create({
            username,
            password,
            email: `${username}@example.com`,
            roles: [role.toInfo()]
        });

        await GSSettingsSpec.makeLogin(username, password, master.key);

    }

    @withTransactionDecorator()
    @test("Get all settings")
    public async getAllSettings() {
        const data: CreateDataBody[] = [
            {
                name: "gs1",
                roundIdRange: ["0", "10000"],
                sessionIdRange: ["0", "10000"],
                description: "settings1Description"
            },
            {
                name: "gs2",
                roundIdRange: ["0", "10000"],
                sessionIdRange: ["0", "10000"],
                description: "settings1Description"
            }];

        await Promise.all(data.map(settings => this.service.create(settings)));

        await request(GSSettingsSpec.server)
            .get("/v1/game-server/settings")
            .set("x-access-token", GSSettingsSpec.token)
            .then((res) => {
                expect(res.status).to.be.equal(200);
                expect(res.body.length).to.be.equal(2);
            });
    }

    @withTransactionDecorator()
    @test("Create settings")
    public async createSettings() {
        const data = {
            name: "gs2",
            roundIdRange: ["0", "10000"],
            sessionIdRange: ["0", "10000"],
            description: "settings1Description"
        };

        await request(GSSettingsSpec.server)
            .post("/v1/game-server/settings")
            .send(data)
            .set("x-access-token", GSSettingsSpec.token)
            .then((res) => {
                expect(res.status).to.be.equal(201);
                expect(res.body.name).to.be.equal(data.name);
            });
    }

    @withTransactionDecorator()
    @test("Get settingsByName")
    public async getSettingsByName() {
        const data: CreateDataBody = {
            name: "gs2",
            roundIdRange: ["0", "10000"],
            sessionIdRange: ["0", "10000"],
            description: "settings1Description"
        };

        await this.service.create(data);

        await request(GSSettingsSpec.server)
            .get(`/v1/game-server/settings/${data.name}`)
            .set("x-access-token", GSSettingsSpec.token)
            .then((res) => {
                expect(res.status).to.be.equal(200);
                expect(res.body.name).to.be.equal(data.name);
            });
    }

    @withTransactionDecorator()
    @test("Update settings")
    public async updateSettings() {
        const data: CreateDataBody = {
            name: "gs2",
            roundIdRange: ["0", "10000"],
            sessionIdRange: ["0", "10000"],
            description: "settings1Description"
        };

        await this.service.create(data);

        await request(GSSettingsSpec.server)
            .put(`/v1/game-server/settings/${data.name}`)
            .send({
                "name": "gsUpdated"
            })
            .set("x-access-token", GSSettingsSpec.token)
            .then((res) => {
                expect(res.status).to.be.equal(200);
                expect(res.body.name).to.be.equal("gsUpdated");
            });
    }

    @withTransactionDecorator()
    @test("Remove settings")
    public async removeSettings() {
        const data: CreateDataBody = {
            name: "gs2",
            roundIdRange: ["0", "10000"],
            sessionIdRange: ["0", "10000"],
            description: "settings1Description"
        };

        await this.service.create(data);

        await request(GSSettingsSpec.server)
            .delete(`/v1/game-server/settings/${data.name}`)
            .set("x-access-token", GSSettingsSpec.token)
            .then((res) => {
                expect(res.status).to.be.equal(204);
            });
    }

    @test("get not existing setting should be rejected")
    public async getNotExistingSettings() {
        await request(GSSettingsSpec.server)
            .get("/v1/game-server/settings/notExisting")
            .set("x-access-token", GSSettingsSpec.token)
            .then((res) => {
                expect(res.status).to.be.equal(404);
                expect(res.body).to.be.deep.equal({
                    message: "Game server settings not found",
                    code: 741
                });
            });
    }

    @test("Update not existing setting should be rejected")
    public async updateNotExistingSettings() {
        await request(GSSettingsSpec.server)
            .put("/v1/game-server/settings/notExisting")
            .set("x-access-token", GSSettingsSpec.token)
            .then((res) => {
                expect(res.status).to.be.equal(404);
                expect(res.body).to.be.deep.equal({
                    message: "Game server settings not found",
                    code: 741
                });
            });
    }

    @test("Remove not existing setting should be rejected")
    public async removeNotExistingSettings() {
        await request(GSSettingsSpec.server)
            .delete("/v1/game-server/settings/notExisting")
            .set("x-access-token", GSSettingsSpec.token)
            .then((res) => {
                expect(res.status).to.be.equal(404);
                expect(res.body).to.be.deep.equal({
                    message: "Game server settings not found",
                    code: 741
                });
            });
    }

    @withTransactionDecorator()
    @test("Create already existing settings should be rejected")
    public async createExistingSettings() {
        const data: CreateDataBody = {
            name: "gs2",
            roundIdRange: ["0", "10000"],
            sessionIdRange: ["0", "10000"],
            description: "settings1Description"
        };

        await this.service.create(data);

        await request(GSSettingsSpec.server)
            .post("/v1/game-server/settings")
            .send(data)
            .set("x-access-token", GSSettingsSpec.token)
            .then((res) => {
                expect(res.status).to.be.equal(409);
                expect(res.body).to.be.deep.equal({
                    message: "Game server settings already exists",
                    code: 742
                });
            });
    }

    @test("Create settings with invalid values should be rejected")
    public async createSettingsWithInvalidParams() {
        const data = {
            roundIdRange: "awd",
            sessionIdRange: ["0", "10000"],
            description: "settings1Description"
        };
        const errorMessage = "Validation error: name should be a non-empty string, " +
            "roundIdRange should be an array of two big numbers and " +
            "the difference between two bounds should not be less then one";

        await request(GSSettingsSpec.server)
            .post("/v1/game-server/settings")
            .send(data)
            .set("x-access-token", GSSettingsSpec.token)
            .then((res) => {
                expect(res.status).to.be.equal(400);
                expect(res.body).to.be.deep.equal({
                    message: errorMessage, //tslint:disable-line
                    code: 40
                });
            });
    }

    @withTransactionDecorator()
    @test("Update settings to existing name should be rejected")
    public async updateSettingsToExitingName() {

        const data: CreateDataBody[] = [
            {
                name: "gs2",
                roundIdRange: ["0", "10000"],
                sessionIdRange: ["0", "10000"],
                description: "settings1Description"
            },
            {
                name: "gs3",
                roundIdRange: ["0", "10000"],
                sessionIdRange: ["0", "10000"],
                description: "settings1Description"
            }];

        await Promise.all(data.map(settings => this.service.create(settings)));

        await request(GSSettingsSpec.server)
            .put("/v1/game-server/settings/gs2")
            .send({
                "name": "gs3"
            })
            .set("x-access-token", GSSettingsSpec.token)
            .then((res) => {
                expect(res.status).to.be.equal(409);
                expect(res.body).to.be.deep.equal({
                    message: "Game server settings already exists",
                    code: 742
                });
            });
    }

    @withTransactionDecorator()
    @test("Update settings with invalid values should be rejected")
    public async updateSettingsWithInvalidParams() {
        const settings: CreateDataBody = {
            name: "gs2",
            roundIdRange: ["0", "10000"],
            sessionIdRange: ["0", "10000"],
            description: "settings1Description"
        };

        await this.service.create(settings);

        const data = {
            roundIdRange: ["0", "10000"],
            sessionIdRange: "awd",
            description: "settings1Description"
        };
        const errorMessage = "Validation error: sessionIdRange should be an array of two big numbers and " +
            "the difference between two bounds should not be less then one";

        await request(GSSettingsSpec.server)
            .put("/v1/game-server/settings/gs2")
            .send(data)
            .set("x-access-token", GSSettingsSpec.token)
            .then((res) => {
                expect(res.status).to.be.equal(400);
                expect(res.body).to.be.deep.equal({
                    message: errorMessage, //tslint:disable-line
                    code: 40
                });
            });
    }
}
