import { application } from "../../skywind/server";
import { complexStructure, truncate } from "../entities/helper";
import EntityCache from "../../skywind/cache/entity";
import { publicId } from "@skywind-group/sw-utils";
import { expect, use, should } from "chai";
import { suite, test, timeout } from "mocha-typescript";
import { BaseApiSuite } from "./base.api";
import * as chaiAsPromised from "chai-as-promised";
import { get as getJurisdictionModel } from "../../skywind/models/jurisdiction";

@suite("EntityJurisdictionSpec API", timeout(20000))
class EntityJurisdictionSpec extends BaseApiSuite {

    public static async before() {
        should();
        use(chaiAsPromised);

        await truncate();
    }

    public before() {
        EntityCache.reset();
    }

    @test()
    public async findAllByPath() {
        const entity = await EntityJurisdictionSpec.factory.create("Entity");
        await EntityJurisdictionSpec.factory.createMany("EntityJurisdiction", 2, {}, {
            entityId: entity.id
        });

        const user = await EntityJurisdictionSpec.factory.create("User");

        const token = await EntityJurisdictionSpec.getAccessToken(
            complexStructure.masterKey, user.username, ["jurisdiction:view"]);

        const response = await EntityJurisdictionSpec.request(await application.get())
            .get(`/v1/entities/${entity.name}/jurisdictions`)
            .set("x-access-token", token)
            .send();

        expect(response.status).to.be.equal(200);
        expect(response.body.length).to.be.equal(2);
    }

    @test()
    public async addJurisdictionToEntity() {
        const [jurisdiction1, ...rest] = await EntityJurisdictionSpec.factory.createMany("Jurisdiction", 2);
        const entity = await EntityJurisdictionSpec.factory.create("Entity");
        const user = await EntityJurisdictionSpec.factory.create("User");

        const token = await EntityJurisdictionSpec.getAccessToken(
            complexStructure.masterKey, user.username, ["jurisdiction:create"]);

        const encodedId = publicId.instance.encode(jurisdiction1.id);
        const response = await EntityJurisdictionSpec.request(await application.get())
            .put(`/v1/entities/${entity.name}/jurisdictions/${jurisdiction1.code}`)
            .set("x-access-token", token)
            .send();

        expect(response.status).to.be.equal(200);
        expect(response.body.id).to.be.equal(encodedId);
    }

    @test()
    public async addJurisdictionToBrand() {
        const entity = await EntityJurisdictionSpec.factory.create("Entity");
        const [jurisdiction, ...rest] = await EntityJurisdictionSpec.factory.createMany("EntityJurisdiction", 2, {}, {
            entityId: entity.id
        });
        const jurisdictionInstance = await getJurisdictionModel()
            .findByPk(jurisdiction.jurisdictionId);

        const user = await EntityJurisdictionSpec.factory.create("User", {}, {
            entityId: entity.id
        });

        const brand = await EntityJurisdictionSpec.factory.create("Brand", {}, {
            parent: entity
        });

        const token = await EntityJurisdictionSpec.getAccessToken(
            entity.key, user.username, ["jurisdiction:create"]);

        const encodedId = publicId.instance.encode(jurisdiction.jurisdictionId);
        const response = await EntityJurisdictionSpec.request(await application.get())
            .put(`/v1/entities/${brand.name}/jurisdictions/${jurisdictionInstance.get("code")}`)
            .set("x-access-token", token)
            .send();

        expect(response.status).to.be.equal(200);
        expect(response.body.id).to.be.equal(encodedId);
    }

    @test()
    public async addTwoJurisdictionToBrand() {
        const entity = await EntityJurisdictionSpec.factory.create("Entity");
        const [jurisdiction, jurisdiction2, ...rest] =
            await EntityJurisdictionSpec.factory.createMany("EntityJurisdiction", 2, {}, {
                entityId: entity.id
            });
        const jurisdictionInstance = await getJurisdictionModel()
            .findByPk(jurisdiction.jurisdictionId);

        const user = await EntityJurisdictionSpec.factory.create("User", {}, {
            entityId: entity.id
        });

        const brand = await EntityJurisdictionSpec.factory.create("Brand", {}, {
            parent: entity
        });

        await EntityJurisdictionSpec.factory.create("EntityJurisdiction", {}, {
            entityId: brand.id,
            jurisdictionId: jurisdiction2.jurisdictionId
        });

        const token = await EntityJurisdictionSpec.getAccessToken(
            entity.key, user.username, ["jurisdiction:create"]);

        const response = await EntityJurisdictionSpec.request(await application.get())
            .put(`/v1/entities/${brand.name}/jurisdictions/${jurisdictionInstance.get("code")}`)
            .set("x-access-token", token)
            .send();

        expect(response.status).to.be.equal(200);
    }

    @test()
    public async addJurisdictionToBrandWhichNotExistInParent() {
        const jurisdiction = await EntityJurisdictionSpec.factory.create("Jurisdiction");

        const entity = await EntityJurisdictionSpec.factory.create("Entity");
        const user = await EntityJurisdictionSpec.factory.create("User", {}, {
            entityId: entity.id
        });

        const brand = await EntityJurisdictionSpec.factory.create("Brand", {}, {
            parent: entity
        });

        const token = await EntityJurisdictionSpec.getAccessToken(
            entity.key, user.username, ["jurisdiction:create"]);

        const encodedId = publicId.instance.encode(jurisdiction.id);
        const response = await EntityJurisdictionSpec.request(await application.get())
            .put(`/v1/entities/${brand.name}/jurisdictions/${jurisdiction.code}`)
            .set("x-access-token", token)
            .send();

        expect(response.status).to.be.equal(400);
        expect(response.body).to.be.deep.equal({
            message: "Validation error: Jurisdiction not exist in parent",
            code: 40
        });
    }

    @test()
    public async removeJurisdictionFromEntity() {
        const user = await EntityJurisdictionSpec.factory.create("User");
        const entity = await EntityJurisdictionSpec.factory.create("Entity");
        const [jurisdiction, ...rest] = await EntityJurisdictionSpec.factory.createMany("EntityJurisdiction", 2, {}, {
            entityId: entity.id
        });
        const jurisdictionInstance = await getJurisdictionModel()
            .findByPk(jurisdiction.jurisdictionId);

        const token = await EntityJurisdictionSpec.getAccessToken(
            complexStructure.masterKey, user.username, ["jurisdiction:delete"]);

        const encodedId = publicId.instance.encode(jurisdiction.jurisdictionId);
        const response = await EntityJurisdictionSpec.request(await application.get())
            .delete(`/v1/entities/${entity.name}/jurisdictions/${jurisdictionInstance.get("code")}`)
            .set("x-access-token", token)
            .send();

        expect(response.status).to.be.equal(204);
    }

    @test()
    public async removeJurisdictionFromBrand() {
        const entity = await EntityJurisdictionSpec.factory.create("Entity");
        const [jurisdiction, ...rest] = await EntityJurisdictionSpec.factory.createMany("EntityJurisdiction", 2, {}, {
            entityId: entity.id
        });
        const jurisdictionInstance = await getJurisdictionModel()
            .findByPk(jurisdiction.jurisdictionId);

        const brand = await EntityJurisdictionSpec.factory.create("Brand", {}, {
            parent: entity
        });

        await EntityJurisdictionSpec.factory.create("EntityJurisdiction", {}, {
            jurisdictionId: jurisdiction.jurisdictionId,
            entityId: brand.id
        });

        const user = await EntityJurisdictionSpec.factory.create("User", {}, {
            entityId: entity.id
        });

        const token = await EntityJurisdictionSpec.getAccessToken(
            entity.key, user.username, ["jurisdiction:delete"]);

        const response = await EntityJurisdictionSpec.request(await application.get())
            .delete(`/v1/entities/${brand.name}/jurisdictions/${jurisdictionInstance.get("code")}`)
            .set("x-access-token", token)
            .send();

        expect(response.status).to.be.equal(400);
        expect(response.body).to.be.deep.equal({
            message: "Validation error: You cannot remove jurisdiction from Brand",
            code: 40
        });
    }

    @test()
    public async removeJurisdictionFromEntityIfJurisdictionInUseByChildren() {
        const user = await EntityJurisdictionSpec.factory.create("User");
        const entity = await EntityJurisdictionSpec.factory.create("Entity");
        const [jurisdiction, ...rest] = await EntityJurisdictionSpec.factory.createMany("EntityJurisdiction", 2, {}, {
            entityId: entity.id
        });
        const jurisdictionInstance = await getJurisdictionModel()
            .findByPk(jurisdiction.jurisdictionId);

        const brand = await EntityJurisdictionSpec.factory.create("Brand", {}, {
            parent: entity
        });

        await EntityJurisdictionSpec.factory.create("EntityJurisdiction", {}, {
            jurisdictionId: jurisdiction.jurisdictionId,
            entityId: brand.id
        });

        const token = await EntityJurisdictionSpec.getAccessToken(
            complexStructure.masterKey, user.username, ["jurisdiction:delete"]);

        const response = await EntityJurisdictionSpec.request(await application.get())
            .delete(`/v1/entities/${entity.name}/jurisdictions/${jurisdictionInstance.get("code")}`)
            .set("x-access-token", token)
            .send();

        expect(response.status).to.be.equal(400);
        expect(response.body).to.be.deep.equal({
            message: "Validation error: Jurisdiction exist in child entity",
            code: 40
        });

    }
}
