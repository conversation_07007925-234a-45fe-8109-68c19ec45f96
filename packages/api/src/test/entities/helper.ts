import "../../skywind/wallet";
import * as redis from "../../skywind/storage/redis";
import { BaseEntity, Entity } from "../../skywind/entities/entity";
import * as GameProviderService from "../../skywind/services/gameprovider";
import * as EntityService from "../../skywind/services/entity";
import * as UserService from "../../skywind/services/user/user";
import * as PermissionService from "../../skywind/services/permission";
import { GameDescription, GameDescriptionByLocale, GameInfo } from "../../skywind/entities/game";
import { GameProviderInfo } from "../../skywind/entities/gameprovider";
import { LimitsByCurrencyCode } from "../../skywind/entities/gamegroup";
import { sequelize as db } from "../../skywind/storage/db";
import { Sequelize } from "sequelize";
import * as CLS from "cls-hooked";
import { DBHelper } from "../../skywind/models/dbHelper";
import EntityCache from "../../skywind/cache/entity";
import * as merchantTypeCache from "../../skywind/cache/merchantTypes";
import * as MerchantCache from "../../skywind/cache/merchant";
import getEntityFactory from "../../skywind/services/entityFactory";
import { defineFactories } from "../factoryGirlHelper";
import { Models } from "../../skywind/models/models";
import { internalAdapters } from "@skywind-group/sw-management-adapters";
import { getSchemaDefinitionService } from "../../skywind/services/gameLimits/schemaDefinition";
import { getSchemaConfigurationService } from "../../skywind/services/gameLimits/schemaConfiguration";
import { resetMerchantsGGRCacheForDefaultCurrencyCache } from "../../skywind/cache/merchantBalance";
import * as FactoryGirl from "factory-girl";
import config from "../../skywind/config";
import { logging } from "@skywind-group/sw-utils";
import { ACTION_METHOD } from "../../skywind/utils/common";
import { AuditableRequest } from "../../skywind/utils/auditHelper";
import * as AuditSummaryCache from "../../skywind/cache/auditSummary";
import { Op } from "sequelize";
import { DynamicDomain } from "../../skywind/entities/domain";
import * as DynamicDomainCache from "../../skywind/cache/dynamicDomainCache";
import * as StaticDomainCache from "../../skywind/cache/staticDomainCache";
import * as DynamicDomainPoolCache from "../../skywind/cache/dynamicDomainPoolCache";
import * as StaticDomainPoolCache from "../../skywind/cache/staticDomainPoolCache";
import * as LobbyCache from "../../skywind/cache/lobby";
import { favoriteGamesCache, gameCategoryGamesCache } from "../../skywind/services/gameCategory/gameCategoryGamesService";

export const US_IP = "**********";
export const CN_IP = "***************";
export const BY_IP = "*************";
export const IE_IP = "**************";

export const MASTER_USER = "SUPERADMIN";

/**
 * truncate - helper function for testing that recreate table
 *
 */
let isInitialized = false;
const dbHelper: DBHelper = new DBHelper();
const testTrxContext = CLS.createNamespace("testTrxContext");

export async function truncate(flushRedis: boolean = true): Promise<void> {
    if (!isInitialized) {
        logging.setUpOutput({ type: config.loggingOutput, logLevel: config.logLevel });
        Sequelize.useCLS(testTrxContext);

        await dbHelper.drop();
        await dbHelper.sync();
        defineFactories();
        isInitialized = true;
    }
    await dbHelper.truncate(db);
    await createDefaultMerchantTypes();

    if (flushRedis) {
        await flushAll();
    }

    await PermissionService.initDB();
    await EntityService.initDB();
    await UserService.initDB();
    await FactoryGirl.factory.create("Jurisdiction",
        {},
        { code: "COM", title: "COM", allowedJackpotConfigurationLevel: 99 });

    merchantTypeCache.reset();
    EntityCache.reset();
    MerchantCache.reset();
    AuditSummaryCache.reset();
    getSchemaDefinitionService().resetCache();
    getSchemaConfigurationService().resetCache();
    resetMerchantsGGRCacheForDefaultCurrencyCache();

    LobbyCache.reset();
    favoriteGamesCache.reset();
    gameCategoryGamesCache.reset();

    DynamicDomainPoolCache.reset();
    StaticDomainPoolCache.reset();
    DynamicDomainCache.reset();
    StaticDomainCache.reset();
}

export const withTransaction = dbHelper.runInTransaction(testTrxContext, db);

const MOCHA_TS_PREFIX = "__mts";

const copyMTSInfo = (target, source) => {
    for (const prop of Object.getOwnPropertyNames(source)) {
        if (prop.startsWith(MOCHA_TS_PREFIX) && !target.hasOwnProperty(prop)) {
            target[prop] = source[prop];
        }
    }
    return target;
};

export function withTransactionDecorator() {
    return (target, key, descriptor) => {
        const originalMethod = descriptor.value;

        const wrapper = async function () {
            await withTransaction(originalMethod.bind(this))();
        };

        descriptor.value = copyMTSInfo(wrapper, originalMethod);

        return descriptor;
    };
}

export let complexStructure = {
    masterKey: "aaa11200-19f1-48c1-a78c-3a3d56095f38",
    // TLE1
    tle1: {
        name: "TLE1",
        description: "TLE1 description",
        defaultCurrency: "USD",
        defaultCountry: "US",
        defaultLanguage: "en",
        key: "71E01000-0000-49e7-87fb-cd9d97f9ac41",
        jurisdictionCode: "COM",
        merchantTypes: ["ipm", "mrch", "mrch_json"]
    },
    // TLE1>ENT1
    tle1ent1: {
        name: "ENT1",
        description: "ENT1 description",
        defaultCurrency: "USD",
        defaultCountry: "US",
        defaultLanguage: "en",
        key: "71E01000-E471-42f9-ad2c-57190e87df62",
        jurisdictionCode: "COM",
        merchantTypes: ["ipm", "mrch", "mrch_json"]
    },
    // TLE1>ENT2
    tle1ent2: {
        name: "ENT2",
        description: "ENT2 description",
        defaultCurrency: "USD",
        defaultCountry: "US",
        defaultLanguage: "en",
        key: "71E01000-E472-43f8-bbfb-fa0513e120a5",
        jurisdictionCode: "COM",
        merchantTypes: ["ipm", "mrch", "mrch_json"]
    },
    // TLE2
    tle2: {
        name: "TLE2",
        description: "TLE2 description",
        defaultCurrency: "USD",
        defaultCountry: "US",
        defaultLanguage: "en",
        key: "71E02000-0000-41c8-9820-5b75a89501ac",
        jurisdictionCode: "COM",
        merchantTypes: ["ipm", "mrch", "mrch_json"]
    },
    // TLE2>ENT1
    tle2ent1: {
        name: "ENT1",
        description: "ENT1 description",
        defaultCurrency: "USD",
        defaultCountry: "US",
        defaultLanguage: "en",
        key: "71E02000-E471-4f9b-9606-e101d6ebf57e",
        jurisdictionCode: "COM",
        merchantTypes: ["ipm", "mrch", "mrch_json"]
    },
    // TLE2>ENT3
    tle2ent3: {
        name: "ENT3",
        description: "ENT3 description",
        defaultCurrency: "USD",
        defaultCountry: "US",
        defaultLanguage: "en",
        key: "71E02000-E473-4013-acc7-bf6b7c96a7e9",
        jurisdictionCode: "COM",
        merchantTypes: ["ipm", "mrch", "mrch_json"]
    },

};

export async function createComplexStructure() {
    await createDefaultMerchantTypes();

    const masterEntity: BaseEntity = await getMasterEntity();

    const masterFactory = getEntityFactory(masterEntity);

    const tle1: Entity = await masterFactory.createEntity(complexStructure.tle1) as Entity;

    const tle1Factory = getEntityFactory(tle1);
    await tle1Factory.createEntity(complexStructure.tle1ent1);
    await tle1Factory.createEntity(complexStructure.tle1ent2);

    const tle2: Entity = await masterFactory.createEntity(complexStructure.tle2) as Entity;

    const tle2Factory = getEntityFactory(tle2);
    await tle2Factory.createEntity(complexStructure.tle2ent1);
    await tle2Factory.createEntity(complexStructure.tle2ent3);

    return masterEntity;
}

let randomCounter = 0;

export async function createRandomGameProvider(
    mustStoreExtHistory: boolean = false,
    code?: string
): Promise<GameProviderInfo> {
    const providerCode = code || "provider" + randomCounter;
    randomCounter += 1;
    return createGameProvider("providerUser" + randomCounter, providerCode, "Provider "
        + randomCounter, "providerSecret" + randomCounter, true, mustStoreExtHistory);
}

export async function createGameProvider(providerUser: string,
    providerCode: string,
    providerTitle: string,
    providerSecret: string,
    providerIsTest: boolean = true,
    mustStoreExtHistory?: boolean): Promise<GameProviderInfo> {
    const master: BaseEntity = await getMasterEntity();

    const data: GameProviderService.CreateProviderData = {
        user: providerUser,
        code: providerCode,
        title: providerTitle,
        secret: providerSecret,
        isTest: providerIsTest,
        mustStoreExtHistory
    };

    return GameProviderService.create(master, data);
}

export async function registerRandomGame(providerCode: string,
    gameType?: string,
    limits?: any,
    physicalTableId?: string,
    features?: any): Promise<GameInfo> {
    randomCounter += 1;
    let suffix = randomCounter.toString();
    while (suffix.length < 3) {
        suffix = "0" + suffix;
    }
    return registerGame(providerCode, "GAME_" + suffix, "Game " + suffix,
        undefined, undefined, features,
        undefined, gameType, limits, physicalTableId);
}

export async function registerGame(providerCode: string,
    providerGameCode: string,
    gameTitle: string,
    url?: string,
    historyRenderType?: number,
    features?: any,
    gameCode?: string,
    gameType: string = "slot",
    limitsData?: LimitsByCurrencyCode,
    physicalTableId?: string): Promise<GameInfo> {
    const defaultInfo: GameDescription = {
        name: "Aztak Slot", description: "Aztak slot is …",
    };
    const info: GameDescriptionByLocale = {
        "EN": {
            name: "Aztak Slot", description: "Aztak slot is …",
        },
        "ZH": {
            name: "...", description: "...",
        },
    };
    if (limitsData === undefined) {
        limitsData = {
            "USD": {
                maxTotalStake: 100,
                stakeAll: [0.1, 0.5, 1, 2, 3, 5],
                stakeDef: 1,
                stakeMax: 5,
                stakeMin: 0.1,
                winMax: 2000,
            },
        };
    }

    const provider = await GameProviderService.findOne({ code: { [Op.eq]: providerCode } });
    const game = await GameProviderService.register({
        type: gameType,
        title: gameTitle,
        url: url === undefined ? "http://url.test" : url,
        gameCode: gameCode || providerGameCode,
        providerGameCode,
        defaultInfo,
        info,
        limits: limitsData,
        providerId: provider.id,
        historyRenderType,
        features,
        physicalTableId,
    });
    return game.toInfo();
}

export async function flushAll(): Promise<void> {
    const client = await redis.get();
    try {
        await client.flushall();
    } finally {
        redis.release(client);
    }
}

export const createAuditableRequest = (
    entity: BaseEntity,
    username: string = "test",
    args: any = {}
): AuditableRequest => {
    return {
        url: "someUrl",
        path: "/some/path",
        body: {
            testData: "some data",
            password: "some password",
            accessToken: "some access token"
        },
        swagger: {
            operation: {
                tags: ["some entity"],
                summary: "some summary",
            },
            apiPath: "/some/path",
        },
        method: ACTION_METHOD.POST,
        resolvedIp: "0.0.0.0",
        headers: {
            userAgent: "Test user agent"
        },
        res: {
            statusCode: 0
        },
        params: {
            someParameter: "test"
        },
        username,
        keyEntity: entity,
        ...args
    };
};

export async function setDynamicDomain(entity: BaseEntity, domain: DynamicDomain) {
    entity.dynamicDomainId = domain.id;
    entity.environment = domain.environment;
    await Models.EntityModel.update({ dynamicDomainId: domain.id, environment: domain.environment },
        { where: { id: entity.id } });
}

export async function resetDynamicDomain(entity: BaseEntity) {
    await Models.EntityModel.update({ dynamicDomainId: null, environment: null } as any,
        { where: { id: entity.id } });
    entity.dynamicDomainId = undefined;
    entity.environment = undefined;
}

export async function createDefaultMerchantTypes() {
    const merchantTypes = await Models.MerchantTypeModel.findAll();
    for (const type in internalAdapters) {
        if (!internalAdapters.hasOwnProperty(type)) {
            continue;
        }
        const alreadyCreated = merchantTypes.find(merchantType => merchantType.get("type") === type);
        if (!internalAdapters.hasOwnProperty(type) || alreadyCreated) {
            continue;
        }

        await Models.MerchantTypeModel.create({ type, schema: {} });
    }
}

export async function getMasterEntity(): Promise<BaseEntity> {
    return await EntityService.findOne({ key: complexStructure.masterKey });
}

export const sortElements = (elements, field) => {
    return elements.sort((a, b) => {
        if (a[field] > b[field]) {
            return 1;
        }
        if (a[field] < b[field]) {
            return -1;
        }
        return 0;
    });
};
