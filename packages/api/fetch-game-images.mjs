#!/usr/bin/env node
import { writeFileSync } from "node:fs";

const get = async (url) => {
    return fetch(url, { method: "GET" }).then(response => {
        if (response.status === 401) {
            console.log("Unauthorized");
            process.exit(0);
        }
        return response.json();
    }).catch(error => {
        console.log(error.message);
        process.exit(0);
    });
};
async function fetchGameImages() {
    const body = await get("https://storage.googleapis.com/lobby.stg1.m27613.com/swbo/games/games.json");
    const images = Object.entries(body.games)
        .reduce((result, [key, {images, screenshots, screenshots_hd}]) => (
            {
                ...result,
                [key]: {
                    images,
                    screenshots: Array.isArray(screenshots) ? screenshots : Object.values(screenshots),
                    screenshots_hd: Array.isArray(screenshots_hd) ? screenshots_hd : Object.values(screenshots_hd)
                }
            }
        ), {});
    writeFileSync('resources/game-images.json', JSON.stringify(images, undefined, 2));
}

void fetchGameImages();
