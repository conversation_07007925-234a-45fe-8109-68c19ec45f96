{"/domain-pools/static": {"post": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:static", "domain-pool:static:create"]}], "tags": ["Domain Pool"], "summary": "Creates static domain pools (game client domain pols)", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/DomainPoolData"}}], "responses": {"201": {"description": "Created static domain pool info", "schema": {"$ref": "#/definitions/StaticDomainPool"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:static", "domain-pool:static:view"]}], "tags": ["Domain Pool"], "summary": "Gets list of static domain pools", "responses": {"200": {"description": "List of static domain pools", "schema": {"type": "array", "items": {"$ref": "#/definitions/StaticDomainPool"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/domain-pools/{poolId}/static": {"patch": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:static", "domain-pool:static:edit"]}], "tags": ["Domain Pool"], "summary": "Updates static domain pools (game client domain pols)", "parameters": [{"$ref": "#/parameters/poolId"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/DomainPoolData"}}], "responses": {"201": {"description": "Updated static domain pool info", "schema": {"$ref": "#/definitions/StaticDomainPool"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:static", "domain-pool:static:view"]}], "tags": ["Domain Pool"], "summary": "Gets static domain pool information", "parameters": [{"$ref": "#/parameters/poolId"}], "responses": {"200": {"description": "Static domain pool info", "schema": {"$ref": "#/definitions/StaticDomainPool"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:static", "domain-pool:static:remove"]}], "tags": ["Domain Pool"], "summary": "Deletes static domain pool", "parameters": [{"$ref": "#/parameters/poolId"}], "responses": {"204": {"description": "Static domain pool deleted successfully"}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/domain-pools/{poolId}/static/{domainId}": {"put": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:static", "domain-pool:static:edit"]}], "tags": ["Domain Pool"], "summary": "Adds static domain pool item", "parameters": [{"$ref": "#/parameters/poolId"}, {"$ref": "#/parameters/domainId"}], "responses": {"200": {"description": "Added static domain pool item info", "schema": {"$ref": "#/definitions/StaticDomainPoolItem"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:static", "domain-pool:static:edit"]}], "tags": ["Domain Pool"], "summary": "Removes static domain pool item", "parameters": [{"$ref": "#/parameters/poolId"}, {"$ref": "#/parameters/domainId"}], "responses": {"204": {"description": "Static domain pool item removed successfully"}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/domain-pools/{poolId}/static/lobby/{domainId}": {"put": {"deprecated": true, "security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:static", "domain-pool:static:edit"]}], "tags": ["Domain Pool"], "summary": "Adds static domain pool item", "parameters": [{"$ref": "#/parameters/poolId"}, {"$ref": "#/parameters/domainId"}], "responses": {"200": {"description": "Added static domain pool item info", "schema": {"$ref": "#/definitions/StaticDomainPoolItem"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "delete": {"deprecated": true, "security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:static", "domain-pool:static:edit"]}], "tags": ["Domain Pool"], "summary": "Removes static domain pool item", "parameters": [{"$ref": "#/parameters/poolId"}, {"$ref": "#/parameters/domainId"}], "responses": {"204": {"description": "Static domain pool item removed successfully"}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/domain-pools/{poolId}/static/{domainId}/enable": {"put": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:static", "domain-pool:static:edit"]}], "tags": ["Domain Pool"], "summary": "Enables static domain pool item", "parameters": [{"$ref": "#/parameters/poolId"}, {"$ref": "#/parameters/domainId"}], "responses": {"200": {"description": "Static domain pool item enabled successfully"}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/domain-pools/{poolId}/static/lobby/{domainId}/enable": {"put": {"deprecated": true, "security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:static", "domain-pool:static:edit"]}], "tags": ["Domain Pool"], "summary": "Enables static domain pool item", "parameters": [{"$ref": "#/parameters/poolId"}, {"$ref": "#/parameters/domainId"}], "responses": {"200": {"description": "Static domain pool item enabled successfully"}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/domain-pools/{poolId}/static/{domainId}/disable": {"put": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:static", "domain-pool:static:edit"]}], "tags": ["Domain Pool"], "summary": "Disables static domain pool item", "parameters": [{"$ref": "#/parameters/poolId"}, {"$ref": "#/parameters/domainId"}], "responses": {"200": {"description": "Static domain pool item disabled successfully"}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/domain-pools/{poolId}/static/lobby/{domainId}/disable": {"put": {"deprecated": true, "security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:static", "domain-pool:static:edit"]}], "tags": ["Domain Pool"], "summary": "Disables static domain pool item", "parameters": [{"$ref": "#/parameters/poolId"}, {"$ref": "#/parameters/domainId"}], "responses": {"200": {"description": "Static domain pool item disabled successfully"}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/domain-pools/static": {"get": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:static", "domain-pool:static:view"]}], "tags": ["Domain Pool"], "summary": "Gets entity static domain pool information", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/inherited"}], "responses": {"200": {"description": "Entity static domain pool info", "schema": {"$ref": "#/definitions/StaticDomainPool"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:static", "domain-pool:static:remove"]}], "tags": ["Domain Pool"], "summary": "Removes static domain pool from an entity", "parameters": [{"$ref": "#/parameters/path"}], "responses": {"204": {"description": "Static domain pool successfully removed from entity"}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/domain-pools/{poolId}/static": {"put": {"security": [{"apiKey": []}, {"Permissions": ["domain-pool", "domain-pool:static", "domain-pool:static:edit"]}], "tags": ["Domain Pool"], "summary": "Adds static domain pool to an entity", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/poolId"}], "responses": {"200": {"description": "Entity static domain pool info", "schema": {"$ref": "#/definitions/StaticDomainPool"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}}