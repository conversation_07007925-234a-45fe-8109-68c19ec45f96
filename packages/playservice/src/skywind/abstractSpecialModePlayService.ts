import { DeferredPayment } from "@skywind-group/sw-deferred-payment";
import { PlayerGameFreebet } from "@skywind-group/sw-management-promo-wallet";
import { AccountPropertiesFilter, PlayerWalletImpl } from "@skywind-group/sw-management-wallet";
import {
    BalanceRequestWithoutToken,
    FinalizeGameRequest,
    FreeBetInfo,
    FreeBetInfoRequest,
    GameLoginRequest,
    GameLogoutRequest,
    GameLogoutResponse,
    GameTokenData,
    MerchantGameTokenData,
    PaymentRequest,
    PaymentRequestWithoutToken,
    PlayerRegulatoryActionRequest,
    RollbackBetRequest,
    RollbackWithoutTokenRequest
} from "@skywind-group/sw-wallet-adapter-core";
import { BaseMerchantPlayService } from "./baseMerchantPlayService";
import { PlayServiceErrors } from "./errors";
import { MerchantPlayer } from "./merchantPlayer";
import { Balance, Balances, OperatorDetails, PlayService } from "./playService";
import { isOfflineFinalizationPayment } from "./playServiceImpl";
import {
    BonusCoinsRedeemRequest,
    DeferredPaymentOperation,
    ExternalTransferData,
    GamePaymentOperation,
    TransferRequest
} from "./request";

export abstract class AbstractSpecialModePlayService extends BaseMerchantPlayService implements PlayService {

    constructor(operator: OperatorDetails,
                protected readonly playService: PlayService,
                protected readonly modeName: string) {
        super(operator);
    }

    public async commitGamePayment(gameTokenData: MerchantGameTokenData,
                                   request: PaymentRequest,
                                   operationId?: number,
                                   filters?: AccountPropertiesFilter[]): Promise<Balance> {

        const player = new MerchantPlayer(this.merchant, this.getWalletKey(gameTokenData));
        return this.sendGamePaymentToMerchant(player, request, gameTokenData, filters);
    }

    public async commitPaymentOperation(gameTokenData: MerchantGameTokenData,
                                        request: GamePaymentOperation,
                                        filters?: AccountPropertiesFilter[]): Promise<Balance> {
        if (isOfflineFinalizationPayment(request)) {
            return { main: 0 };
        }
        const actions = request.actions;

        if (!Array.isArray(actions) || actions.length === 0) {
            return Promise.reject(new PlayServiceErrors.PaymentActionListIsEmpty());
        }

        let totalBet: number = 0;
        let totalWin: number = 0;

        for (const item of actions) {
            const action = item.action;
            const amount = item.amount;
            if (!Number.isFinite(amount) || amount < 0) {
                return Promise.reject(new PlayServiceErrors.InvalidPaymentActionValueError());
            }
            switch (action) {
                case "debit":
                    if (request.operation === "win") {
                        return Promise.reject(new PlayServiceErrors.InvalidPaymentActionTypeError(action +
                            " for credit operation"));
                    }
                    totalBet += amount;
                    break;
                case "credit":
                    if (request.operation === "bet") {
                        return Promise.reject(new PlayServiceErrors.InvalidPaymentActionTypeError(action +
                            " for debit operation"));
                    }
                    totalWin += amount;
                    break;
                default:
                    return Promise.reject(new PlayServiceErrors.InvalidPaymentActionTypeError(action));
            }
        }

        const payment: PaymentRequest = { ...request } as any;
        payment.bet = totalBet;
        payment.win = totalWin;

        const player = new MerchantPlayer(this.merchant, this.getWalletKey(gameTokenData));

        let resultBalance: Balance;
        if (request.operation === "bet") {
            resultBalance = await player.commitBetPayment(gameTokenData, payment, filters);
        } else if (request.operation === "win") {
            resultBalance = await player.commitWinPayment(gameTokenData, payment, filters);
        } else {
            resultBalance = await player.commitPayment(gameTokenData, payment, filters);
        }

        return resultBalance;
    }

    public async commitWinWithoutToken(request: PaymentRequestWithoutToken,
                                       filters?: AccountPropertiesFilter[]): Promise<Balance> {
        return this.merchant.commitWinWithoutToken(request);
    }

    public async getGameBalance(gameTokenData: MerchantGameTokenData,
                                filters?: AccountPropertiesFilter[]): Promise<Balance> {
        return this.playService.getGameBalance(gameTokenData, filters);
    }

    public async getPlayerBalanceWithoutToken(req: BalanceRequestWithoutToken,
                                              filters?: AccountPropertiesFilter[]): Promise<Balance> {
        return this.playService.getPlayerBalanceWithoutToken(req);
    }

    public async getPlayerBalances(gameTokenData: MerchantGameTokenData,
                                   filters?: AccountPropertiesFilter[]): Promise<Balances> {
        return this.playService.getPlayerBalances(gameTokenData, filters);
    }

    public async loginGame(gameTokenData: GameTokenData, request: GameLoginRequest): Promise<void> {
        return this.playService.loginGame(gameTokenData, request);
    }

    public async logoutGame(gameTokenData: GameTokenData, request: GameLogoutRequest): Promise<GameLogoutResponse> {
        return this.playService.logoutGame(gameTokenData, request);
    }

    public async finalizeGame(gameTokenData: GameTokenData, request: FinalizeGameRequest): Promise<Balance | void> {
        return this.playService.finalizeGame(gameTokenData, request);
    }

    public async refundBet(gameTokenData: MerchantGameTokenData,
                           request,
                           filters?: AccountPropertiesFilter[]): Promise<Balance> {
        return this.merchant.refundBetPayment(gameTokenData, request);
    }

    protected getWalletKey(gameTokenData: GameTokenData): string {
        return PlayerWalletImpl.toWalletKey(gameTokenData.brandId, gameTokenData.playerCode, gameTokenData.currency);
    }

    public async performRegulatoryAction(gameTokenData: GameTokenData,
                                         request: PlayerRegulatoryActionRequest): Promise<void> {
        throw new PlayServiceErrors.UnsupportedPlayMethodError(`Regulatory Actions not supported for ${this.modeName}`);
    }

    public async redeemBonusCoins(gameTokenData: GameTokenData,
                                  request: BonusCoinsRedeemRequest,
                                  filters?: AccountPropertiesFilter[]): Promise<Balance> {
        throw new PlayServiceErrors.UnsupportedPlayMethodError(`Redeem bonus coins operation not supported for ${this.modeName}`);
    }

    public async rollbackBetPayment(gameTokenData: GameTokenData,
                                    request: RollbackBetRequest,
                                    filters?: AccountPropertiesFilter[]): Promise<Balance> {
        throw new PlayServiceErrors.UnsupportedPlayMethodError(`Rollback operation not supported for ${this.modeName}`);
    }

    public async rollbackBetPaymentWithoutToken(request: RollbackWithoutTokenRequest,
                                                filters?: AccountPropertiesFilter[]): Promise<Balance> {
        throw new PlayServiceErrors.UnsupportedPlayMethodError(`Rollback operation not supported for ${this.modeName}`);
    }

    public async transferBalance(gameTokenData: GameTokenData, request: TransferRequest): Promise<Balance> {
        throw new PlayServiceErrors.UnsupportedPlayMethodError(`Transfer operation not supported for ${this.modeName}`);
    }

    public async externalTransfer(gameTokenData: GameTokenData, request: ExternalTransferData): Promise<Balance> {
        throw new PlayServiceErrors.UnsupportedPlayMethodError(`External transfer operation not supported for ${this.modeName}`);
    }

    public async getFreeBetInfo(gameTokenData: GameTokenData,
                                request: FreeBetInfoRequest,
                                rewards?: PlayerGameFreebet[]): Promise<FreeBetInfo> {
        throw new PlayServiceErrors.UnsupportedPlayMethodError(`FreeBet operations not supported for ${this.modeName}`);
    }

    public async commitDeferredPayment(gameTokenData: GameTokenData, request: DeferredPaymentOperation,
                                       gameBalanceFilter: AccountPropertiesFilter[]): Promise<Balance> {
        return Promise.reject(new PlayServiceErrors.UnsupportedPlayMethodError());
    }

    public async commitOfflineDeferredPayment(promoId: string, payment: DeferredPayment): Promise<void> {
        throw new PlayServiceErrors.UnsupportedPlayMethodError();
    }

    public async keepAlive(gameTokenData: MerchantGameTokenData): Promise<void> {
        throw new PlayServiceErrors.UnsupportedPlayMethodError();
    }
}
