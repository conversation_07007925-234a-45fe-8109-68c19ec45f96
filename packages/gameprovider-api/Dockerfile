######################### SONAR STUFF #########################
FROM asia.gcr.io/gcpstg/lerna-builder:node_22 as build

WORKDIR /app
COPY . /app/

RUN corepack enable && pnpm install --no-frozen-lockfile  \
    && pnpm run clean  \
    && pnpm run compile  \
    && pnpm run version \

CMD ["/bin/cat", "/app/out/skywind/version"]
CMD ["node", "/app/out/skywind/appGameProvider.js"]

######################### MAIN IMAGE #########################
FROM node:22.18.0-alpine as main

EXPOSE 3000
WORKDIR /app

COPY --chown=node:node --from=build /app/package.json /app/pnpm-lock.yaml /app/swagger-gameprovider.json /app/swagger-gameprovider-play-v2.json /app/.npmrc ./
COPY --chown=node:node --from=build /app/swagger-resources ./swagger-resources
COPY --chown=node:node --from=build /app/swagger-ui-gameprovider ./swagger-ui-gameprovider
COPY --chown=node:node --from=build /app/swagger-ui-gameprovider-v2 ./swagger-ui-gameprovider-v2
COPY --chown=node:node --from=build /app/out ./out

RUN corepack enable \
    && pnpm install --frozen-lockfile --prod \
    && pnpm store prune \
    && rm .npmrc \
    && rm -fr /root/.cache \
    && corepack disable

USER node
CMD ["node", "/app/out/skywind/appGameProvider.js"]
