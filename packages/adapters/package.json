{"name": "@skywind-group/sw-management-adapters", "version": "2.141.0", "description": "Adapters (seamless, pop, gvc):", "license": "ISC", "author": "<PERSON> <<EMAIL>>", "main": "lib/index.js", "typings": "lib/index.d.ts", "scripts": {"clean": "rm -rf ./lib", "compile": "tsc -b tsconfig.build.json", "version": "mkdir -p lib/skywind && echo $(node -p \"require('./package.json').version\") $( git log --pretty=format:'%h' -n 1) $(date) > ./lib/skywind/version"}, "dependencies": {"@skywind-group/sw-adapter-regulation-support": "^1.0.2", "@skywind-group/sw-currency-exchange": "2.3.19", "@skywind-group/sw-management-playservice": "workspace:~2.141.0", "@skywind-group/sw-management-promo-wallet": "workspace:~2.141.0", "@skywind-group/sw-management-wallet": "workspace:~2.141.0", "@skywind-group/sw-pop-notification": "0.1.0", "@skywind-group/sw-wallet-adapter-core": "2.1.9", "ioredis": "5.5.0", "properties": "1.2.1", "request": "2.88.0", "shortid": "2.2.16", "uuid": "9.0.1"}, "devDependencies": {"@skywind-group/sw-messaging": "0.2.4"}}