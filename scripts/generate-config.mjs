#!/usr/bin/env node
import fg from "fast-glob";
import fs from "fs/promises";

async function generateConfig() {
    const packageJsonPaths = await fg(["packages/*/package.json"]);
    await generateTSConfig(packageJsonPaths);
    await updateProjectRelease(packageJsonPaths);
}

async function generateTSConfig(packages) {
    const tsCommonConfigRaw = await fs.readFile("./tsconfig.build.json", { encoding: "utf-8" });
    const tsCommonConfig = JSON.parse(tsCommonConfigRaw);

    for (const pkgPath of packages) {
        const pkgRaw = await fs.readFile(pkgPath, { encoding: "utf-8" });
        const pkg = JSON.parse(pkgRaw);
        if (pkg.private) {
            const tsPath = pkgPath.replace("package.json", "tsconfig.build.json");
            const tsConfigRaw = await fs.readFile(tsPath, { encoding: "utf-8" });
            const tsConfig = JSON.parse(tsConfigRaw);
            delete tsConfig.extends;
            tsConfig.compilerOptions = { ...tsCommonConfig.compilerOptions, ...tsConfig.compilerOptions };
            const config = { ...tsCommonConfig, ...tsConfig };

            await fs.writeFile(tsPath, JSON.stringify(config, null, 2) + "\n");
        }
    }
}

async function updateProjectRelease(packages) {
    const apis = new Map();
    const libs = new Map();
    for (const pkgPath of packages) {
        const pkgRaw = await fs.readFile(pkgPath, 'utf-8');
        const pkg = JSON.parse(pkgRaw);
        if (pkg.private) {
            apis.set(pkg.name, { pkgPath, json: pkg });
        } else {
            libs.set(pkg.name, pkg.version);
        }
    }

    for (let { pkgPath, json } of apis.values()) {
        for (const [ key, version ] of libs.entries()) {
            if (json?.dependencies?.[key]) {
                json.dependencies[key] = `~${version}`;
            }
            if (json?.devDependencies?.[key]) {
                json.devDependencies[key] = `~${version}`;
            }
            if (json?.peerDependencies?.[key]) {
                json.peerDependencies[key] = `~${version}`;
            }
        }
        await fs.writeFile(pkgPath, JSON.stringify(json, null, 2) + '\n');
    }

    console.log("🎉 Release field updated in all relevant packages.");
}

void generateConfig();
