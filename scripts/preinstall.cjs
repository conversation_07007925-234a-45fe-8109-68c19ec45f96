const versions = process.versions.node.split(".");
const version = +(versions?.[0]);
// Check node version
if (version < 22) {
    console.error(
        'Please make sure that your installed Node version is greater than v22'
    );
    process.exit(1);
}

const message = `
╔══════════════════════════════════════════════════════════════════╗
║                                                                  ║
║   Use "pnpm install" for installation in this project.           ║
║                                                                  ║
║   If you don't have pnpm, enable corepack via "corepack enable". ║
║   Then run "pnpm install" to install dependencies.               ║
║                                                                  ║
╚══════════════════════════════════════════════════════════════════╝
`;

const used_pnpm = process.env.npm_config_user_agent.startsWith(`pnpm`);
if (!used_pnpm) {
    console.error(message);
    process.exit(1);
}
