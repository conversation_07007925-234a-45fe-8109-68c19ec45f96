#!/usr/bin/env node
import fg from "fast-glob";
import fs from "fs/promises";

// example: lib_version=0.6.95 lib_name=sw-wallet-adapter-core npm run sync-lib
async function syncLibrary() {
    if (!process.env.lib_version || !process.env.lib_name) {
        return ;
    }
    const packageJsonPaths = await fg(["./package.json", "packages/*/package.json"]);
    for (const pkgPath of packageJsonPaths) {
        const pkgRaw = await fs.readFile(pkgPath, { encoding: "utf-8" });
        const pkg = JSON.parse(pkgRaw);

        const key = `@skywind-group/${process.env.lib_name}`;
        if (pkg?.dependencies?.[key]) {
            pkg.dependencies[key] = process.env.lib_version;
        }
        if (pkg?.devDependencies?.[key]) {
            pkg.devDependencies[key] = process.env.lib_version;
        }
        if (pkg?.peerDependencies?.[key]) {
            pkg.peerDependencies[key] = process.env.lib_version;
        }

        console.log("Updated:", pkg.name);
        await fs.writeFile(pkgPath, JSON.stringify(pkg, null, 2) + "\n");
    }
    console.log("🎉 Synced library");
}

void syncLibrary().catch((err) => {
    console.error("❌ Failed to sync lib:", err);
    process.exit(1);
});
