#!/usr/bin/env node

import { readFileSync, readdirSync, statSync } from 'fs';
import { join, extname, relative } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

/**
 * Validates JSON syntax
 * @param {string} filePath - Path to the JSON file
 * @returns {Object} - Validation result
 */
function validateJsonSyntax(filePath) {
    try {
        const content = readFileSync(filePath, 'utf8');
        JSON.parse(content);
        return { valid: true, error: null };
    } catch (error) {
        return { 
            valid: false, 
            error: `JSON syntax error: ${error.message}` 
        };
    }
}

/**
 * Check if this is a partial swagger file (part of mapi-swagger directory)
 * @param {string} filePath - Path to the file
 * @returns {boolean} - True if it's a partial file
 */
function isPartialSwaggerFile(filePath) {
    return filePath.includes('mapi-swagger') ||
           filePath.includes('definitions') ||
           filePath.includes('parameters');
}

/**
 * Basic Swagger/OpenAPI structure validation
 * @param {string} filePath - Path to the swagger file
 * @returns {Object} - Validation result
 */
function validateSwaggerStructure(filePath) {
    try {
        const content = readFileSync(filePath, 'utf8');
        const swagger = JSON.parse(content);
        const errors = [];
        const warnings = [];

        // Skip validation for partial swagger files
        if (isPartialSwaggerFile(filePath)) {
            // For partial files, just validate JSON structure and basic content
            if (typeof swagger !== 'object' || swagger === null) {
                errors.push('File must contain a valid JSON object');
            }
            return {
                valid: errors.length === 0,
                errors,
                warnings: ['Partial swagger file - skipping full validation'],
                isPartial: true
            };
        }

        // Check for required Swagger 2.0 fields
        if (swagger.swagger) {
            // Swagger 2.0 validation
            if (!swagger.info) {
                errors.push('Missing required field: info');
            }
            if (!swagger.paths) {
                errors.push('Missing required field: paths');
            }
            if (swagger.swagger !== '2.0') {
                warnings.push(`Swagger version is ${swagger.swagger}, expected 2.0`);
            }
        } else if (swagger.openapi) {
            // OpenAPI 3.x validation
            if (!swagger.info) {
                errors.push('Missing required field: info');
            }
            if (!swagger.paths) {
                errors.push('Missing required field: paths');
            }
        } else {
            errors.push('Missing swagger or openapi version field');
        }

        // Check info object structure
        if (swagger.info) {
            if (!swagger.info.title) {
                errors.push('Missing required field: info.title');
            }
            if (!swagger.info.version) {
                errors.push('Missing required field: info.version');
            }
        }

        // Check paths structure
        if (swagger.paths) {
            for (const [path, pathItem] of Object.entries(swagger.paths)) {
                if (typeof pathItem !== 'object' || pathItem === null) {
                    errors.push(`Invalid path item for ${path}: must be an object`);
                    continue;
                }

                // Check HTTP methods
                const validMethods = ['get', 'post', 'put', 'delete', 'options', 'head', 'patch', 'trace'];
                for (const [method, operation] of Object.entries(pathItem)) {
                    if (method === 'parameters' || method === '$ref') {
                        continue; // Skip path-level parameters and $ref
                    }
                    
                    if (!validMethods.includes(method.toLowerCase())) {
                        warnings.push(`Unknown HTTP method '${method}' in path ${path}`);
                        continue;
                    }

                    if (typeof operation !== 'object' || operation === null) {
                        errors.push(`Invalid operation for ${method.toUpperCase()} ${path}: must be an object`);
                        continue;
                    }

                    // Check operation structure
                    if (!operation.responses) {
                        errors.push(`Missing responses for ${method.toUpperCase()} ${path}`);
                    }
                }
            }
        }

        // Check definitions/components (if present)
        if (swagger.definitions) {
            for (const [defName, definition] of Object.entries(swagger.definitions)) {
                if (typeof definition !== 'object' || definition === null) {
                    errors.push(`Invalid definition '${defName}': must be an object`);
                }
            }
        }

        if (swagger.components && swagger.components.schemas) {
            for (const [schemaName, schema] of Object.entries(swagger.components.schemas)) {
                if (typeof schema !== 'object' || schema === null) {
                    errors.push(`Invalid schema '${schemaName}': must be an object`);
                }
            }
        }

        return {
            valid: errors.length === 0,
            errors,
            warnings,
            isPartial: false
        };
    } catch (error) {
        return {
            valid: false,
            errors: [`Failed to parse swagger file: ${error.message}`],
            warnings: []
        };
    }
}

/**
 * Find all swagger/JSON files in a directory
 * @param {string} dir - Directory to search
 * @param {Array} extensions - File extensions to include
 * @returns {Array} - Array of file paths
 */
function findSwaggerFiles(dir, extensions = ['.json']) {
    const files = [];
    
    try {
        const items = readdirSync(dir);
        
        for (const item of items) {
            const fullPath = join(dir, item);
            const stat = statSync(fullPath);
            
            if (stat.isDirectory()) {
                // Skip node_modules and other common directories
                if (!['node_modules', '.git', 'out', 'coverage', '.nyc_output'].includes(item)) {
                    files.push(...findSwaggerFiles(fullPath, extensions));
                }
            } else if (stat.isFile()) {
                const ext = extname(item);
                if (extensions.includes(ext)) {
                    // Include files that are likely swagger files
                    const fileName = item.toLowerCase();
                    if (fileName.includes('swagger') || 
                        fileName.includes('openapi') || 
                        dir.includes('swagger') ||
                        dir.includes('mapi-swagger')) {
                        files.push(fullPath);
                    }
                }
            }
        }
    } catch (error) {
        console.warn(`Warning: Could not read directory ${dir}: ${error.message}`);
    }
    
    return files;
}

/**
 * Main validation function
 */
function main() {
    console.log('🔍 Starting Swagger/OpenAPI validation...\n');
    
    const swaggerFiles = findSwaggerFiles(rootDir);
    
    if (swaggerFiles.length === 0) {
        console.log('ℹ️  No swagger files found.');
        return;
    }
    
    console.log(`Found ${swaggerFiles.length} swagger files:\n`);
    
    let totalErrors = 0;
    let totalWarnings = 0;
    const results = [];
    
    for (const filePath of swaggerFiles) {
        const relativePath = relative(rootDir, filePath);
        console.log(`📄 Validating: ${relativePath}`);
        
        // First validate JSON syntax
        const jsonResult = validateJsonSyntax(filePath);
        if (!jsonResult.valid) {
            console.log(`  ❌ ${jsonResult.error}`);
            totalErrors++;
            results.push({
                file: relativePath,
                valid: false,
                errors: [jsonResult.error],
                warnings: []
            });
            continue;
        }
        
        // Then validate Swagger structure
        const swaggerResult = validateSwaggerStructure(filePath);
        results.push({
            file: relativePath,
            valid: swaggerResult.valid,
            errors: swaggerResult.errors || [],
            warnings: swaggerResult.warnings || [],
            isPartial: swaggerResult.isPartial || false
        });

        if (swaggerResult.isPartial) {
            if (swaggerResult.valid) {
                console.log('  ✅ Valid (partial file)');
            } else {
                console.log('  ❌ Invalid (partial file)');
            }
        } else {
            if (swaggerResult.valid) {
                console.log('  ✅ Valid');
            } else {
                console.log('  ❌ Invalid');
            }
        }

        // Display errors (only count non-partial file errors for exit code)
        if (swaggerResult.errors && swaggerResult.errors.length > 0) {
            swaggerResult.errors.forEach(error => {
                console.log(`    🔴 Error: ${error}`);
                if (!swaggerResult.isPartial) {
                    totalErrors++;
                }
            });
        }

        // Display warnings
        if (swaggerResult.warnings && swaggerResult.warnings.length > 0) {
            swaggerResult.warnings.forEach(warning => {
                console.log(`    🟡 Warning: ${warning}`);
                totalWarnings++;
            });
        }
        
        console.log('');
    }
    
    // Summary
    const partialFiles = results.filter(r => r.isPartial);
    const fullFiles = results.filter(r => !r.isPartial);

    console.log('📊 Validation Summary:');
    console.log(`  Total files processed: ${swaggerFiles.length}`);
    console.log(`  Full swagger files: ${fullFiles.length} (${fullFiles.filter(r => r.valid).length} valid, ${fullFiles.filter(r => !r.valid).length} invalid)`);
    console.log(`  Partial files: ${partialFiles.length} (${partialFiles.filter(r => r.valid).length} valid, ${partialFiles.filter(r => !r.valid).length} invalid)`);
    console.log(`  Critical errors: ${totalErrors}`);
    console.log(`  Total warnings: ${totalWarnings}`);

    if (totalErrors > 0) {
        console.log('\n❌ Swagger validation failed!');
        console.log('💡 Note: Only errors in full swagger files cause build failure. Partial files are informational.');
        process.exit(1);
    } else {
        console.log('\n✅ All critical swagger validations passed!');
        if (totalWarnings > 0) {
            console.log(`⚠️  Note: ${totalWarnings} warnings found (non-blocking)`);
        }
    }
}

// Run the validation
main();
