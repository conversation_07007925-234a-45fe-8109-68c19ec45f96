#!/bin/bash

release_version=$1
lerna_version=$2

echo "current version: $release_version";
echo "current lerna version: $lerna_version";

git checkout -b "start_${release_version}"
pnpm run prepareRelease --lerna-version="${lerna_version}-develop" --release-version="${release_version}"

RELEASE_VERSION="${release_version}" node scripts/update-swagger-version.js

git add .
git commit -m "Prepare release ${release_version}"
git push -u origin "start_${release_version}"
