#!/usr/bin/env node
import fg from "fast-glob";
import fs from "fs/promises";

async function updateProjectRelease() {
    const packageJsonPaths = await fg(["packages/*/package.json"]);

    const packages = new Map();
    const libs = new Map();
    for (const pkgPath of packageJsonPaths) {
        const pkgRaw = await fs.readFile(pkgPath, { encoding: "utf-8" });
        const pkg = JSON.parse(pkgRaw);
        packages.set(pkg.name, { pkgPath, json: pkg });
        if (!pkg.private) {
            libs.set(pkg.name, pkg.version);
        }
    }
    const releaseVersion = process.env.npm_config_release_version;
    for (let { pkgPath, json } of packages.values()) {
        for (const [ key, version ] of libs.entries()) {
            if (json?.dependencies?.[key]) {
                json.dependencies[key] = `workspace:~${version}`;
            }
            if (json?.devDependencies?.[key]) {
                json.devDependencies[key] = `workspace:~${version}`;
            }
            if (json?.peerDependencies?.[key]) {
                json.peerDependencies[key] = `workspace:~${version}`;
            }
        }
        if (json.release) {
            console.log("Update project release version", pkgPath);
            json.release = releaseVersion;
        }

        await fs.writeFile(pkgPath, JSON.stringify(json, null, 2) + "\n");
    }

    console.log("🎉 Release field updated in all relevant packages.");
}

void updateProjectRelease().catch((err) => {
    console.error("❌ Failed to update project release:", err);
    process.exit(1);
});
